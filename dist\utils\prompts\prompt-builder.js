import { logInfo, logError } from '../logger/log.js';
const VARIABLE_PATTERN = /\{\{([^}]+)\}\}/g;
const PROVIDER_OPTIMIZATIONS = {
    openai: {
        maxTokens: 4096,
        supportsSystemRole: true,
        preferredFormat: 'markdown',
        specialInstructions: 'Use clear, structured responses with proper formatting.'
    },
    anthropic: {
        maxTokens: 8192,
        supportsSystemRole: true,
        preferredFormat: 'markdown',
        specialInstructions: 'Think step by step and provide detailed explanations.'
    },
    deepseek: {
        maxTokens: 4096,
        supportsSystemRole: true,
        preferredFormat: 'markdown',
        specialInstructions: 'Focus on accuracy and provide comprehensive solutions.'
    },
    gemini: {
        maxTokens: 2048,
        supportsSystemRole: false,
        preferredFormat: 'plain',
        specialInstructions: 'Be concise and direct in responses.'
    },
    ollama: {
        maxTokens: 2048,
        supportsSystemRole: true,
        preferredFormat: 'markdown',
        specialInstructions: 'Optimize for local processing efficiency.'
    }
};
export class PromptBuilder {
    template;
    context = {};
    config = null;
    providerOptimization = null;
    sections = [];
    variables = {};
    constructor(template) {
        this.template = template;
    }
    withContext(context) {
        this.context = { ...this.context, ...context };
        return this;
    }
    withConfig(config) {
        this.config = config;
        return this;
    }
    withProviderOptimization(provider) {
        this.providerOptimization = provider;
        return this;
    }
    addSection(title, content) {
        this.sections.push(`# ${title}\n\n${content}`);
        return this;
    }
    withVariables(variables) {
        this.variables = { ...this.variables, ...variables };
        return this;
    }
    async build() {
        try {
            logInfo('Building system prompt', {
                templateId: this.template.id,
                provider: this.providerOptimization
            });
            let prompt = this.template.template;
            const allVariables = this.prepareVariables();
            prompt = this.substituteVariables(prompt, allVariables);
            if (this.sections.length > 0) {
                prompt += '\n\n' + this.sections.join('\n\n');
            }
            if (this.providerOptimization) {
                prompt = this.applyProviderOptimizations(prompt, this.providerOptimization);
            }
            if (this.config) {
                prompt = this.applyConfigModifications(prompt, this.config);
            }
            prompt = this.finalizePrompt(prompt);
            logInfo('System prompt built successfully', {
                templateId: this.template.id,
                promptLength: prompt.length,
                variableCount: Object.keys(allVariables).length
            });
            return prompt;
        }
        catch (error) {
            logError('Failed to build system prompt', error instanceof Error ? error : new Error(String(error)));
            return this.template.template;
        }
    }
    prepareVariables() {
        const variables = {};
        Object.assign(variables, this.context);
        Object.assign(variables, this.variables);
        variables.timestamp = new Date().toISOString();
        variables.templateId = this.template.id;
        variables.templateVersion = this.template.metadata.version;
        this.flattenObject(variables, variables);
        return variables;
    }
    flattenObject(obj, target, prefix = '') {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const newKey = prefix ? `${prefix}.${key}` : key;
                if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                    this.flattenObject(obj[key], target, newKey);
                }
                else {
                    target[newKey] = obj[key];
                }
            }
        }
    }
    substituteVariables(template, variables) {
        return template.replace(VARIABLE_PATTERN, (match, variablePath) => {
            const value = this.getNestedValue(variables, variablePath.trim());
            if (value !== undefined && value !== null) {
                if (typeof value === 'object') {
                    return JSON.stringify(value, null, 2);
                }
                return String(value);
            }
            return `[${variablePath}]`;
        });
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }
    applyProviderOptimizations(prompt, provider) {
        const optimization = PROVIDER_OPTIMIZATIONS[provider];
        if (!optimization) {
            return prompt;
        }
        let optimizedPrompt = prompt;
        if (optimization.specialInstructions) {
            optimizedPrompt += `\n\n# Provider Optimization\n${optimization.specialInstructions}`;
        }
        if (optimization.preferredFormat === 'plain') {
            optimizedPrompt = optimizedPrompt
                .replace(/^#+\s/gm, '')
                .replace(/\*\*(.*?)\*\*/g, '$1')
                .replace(/\*(.*?)\*/g, '$1');
        }
        if (!optimization.supportsSystemRole) {
            optimizedPrompt = `Instructions: ${optimizedPrompt}`;
        }
        return optimizedPrompt;
    }
    applyConfigModifications(prompt, config) {
        let modifiedPrompt = prompt;
        if (config.safetyLevel) {
            const safetyInstructions = this.getSafetyInstructions(config.safetyLevel);
            modifiedPrompt += `\n\n# Safety Guidelines\n${safetyInstructions}`;
        }
        if (config.verbosityLevel) {
            const verbosityInstructions = this.getVerbosityInstructions(config.verbosityLevel);
            modifiedPrompt += `\n\n# Response Style\n${verbosityInstructions}`;
        }
        if (config.enableThinking) {
            modifiedPrompt += '\n\n# Thinking Process\nThink through problems step by step before providing solutions.';
        }
        if (config.enablePlanning) {
            modifiedPrompt += '\n\n# Planning\nCreate detailed plans for complex tasks before execution.';
        }
        if (config.enableValidation) {
            modifiedPrompt += '\n\n# Validation\nValidate your work and double-check results before finalizing.';
        }
        if (config.customInstructions) {
            modifiedPrompt += `\n\n# Custom Instructions\n${config.customInstructions}`;
        }
        return modifiedPrompt;
    }
    getSafetyInstructions(level) {
        switch (level) {
            case 'strict':
                return 'Always prioritize safety. Refuse potentially harmful requests. Ask for confirmation before executing system commands.';
            case 'moderate':
                return 'Exercise caution with system operations. Warn about potentially risky actions.';
            case 'permissive':
                return 'Proceed with user requests while noting any potential risks.';
            default:
                return 'Follow standard safety practices.';
        }
    }
    getVerbosityInstructions(level) {
        switch (level) {
            case 'minimal':
                return 'Provide concise, direct responses with minimal explanation.';
            case 'normal':
                return 'Provide clear responses with appropriate level of detail.';
            case 'detailed':
                return 'Provide comprehensive responses with detailed explanations.';
            case 'verbose':
                return 'Provide extensive responses with thorough explanations and examples.';
            default:
                return 'Provide clear and helpful responses.';
        }
    }
    finalizePrompt(prompt) {
        return prompt
            .replace(/\n{3,}/g, '\n\n')
            .replace(/^\s+|\s+$/g, '')
            .replace(/\[([^\]]+)\]/g, '')
            + '\n';
    }
}
export async function buildPrompt(template, context = {}, config, provider) {
    const builder = new PromptBuilder(template);
    builder.withContext(context);
    if (config) {
        builder.withConfig(config);
    }
    if (provider) {
        builder.withProviderOptimization(provider);
    }
    return builder.build();
}
