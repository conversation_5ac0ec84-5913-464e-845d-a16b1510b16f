#!/usr/bin/env node

/**
 * Enhanced Agent Loop Demonstration
 * 
 * Demonstrates all the new enhanced features of the agent loop
 */

import { Agent<PERSON>oop } from './dist/utils/agent/agent-loop.js';
import { createInputItem } from './dist/utils/input-utils.js';

async function demonstrateEnhancedFeatures() {
  console.log('🚀 Enhanced Agent Loop Demonstration\n');

  // Create enhanced agent loop with all features enabled
  const agentLoop = new AgentLoop({
    model: 'deepseek-chat',
    provider: 'deepseek',
    approvalPolicy: 'suggest',
    enableStreaming: true,
    enableToolRegistry: true,
    enableSessionPersistence: true,
    enableContextOptimization: true,
    enablePerformanceMonitoring: true,
    maxContextTokens: 1000,
    contextCompressionThreshold: 0.8
  });

  console.log(`📋 Session ID: ${agentLoop.getSessionId()}`);
  console.log(`🔄 Initial State: ${agentLoop.getCurrentState()}`);

  // Set up enhanced callbacks
  const callbacks = {
    onStateChange: (state) => {
      console.log(`🔄 State changed to: ${state}`);
    },
    onPerformanceMetrics: (metrics) => {
      console.log(`📊 Performance Update:`, {
        executionTime: `${metrics.totalExecutionTime}ms`,
        tokenUsage: metrics.tokenUsage,
        toolCalls: metrics.toolCallCount,
        successRate: `${(metrics.successRate * 100).toFixed(1)}%`
      });
    },
    onContextOptimization: (before, after) => {
      console.log(`🗜️  Context optimized: ${before} → ${after} tokens`);
    },
    onSessionSave: (sessionId) => {
      console.log(`💾 Session saved: ${sessionId}`);
    },
    onIterationStart: (iteration) => {
      console.log(`🔄 Starting iteration ${iteration}`);
    },
    onIterationComplete: (iteration, result) => {
      console.log(`✅ Completed iteration ${iteration}`);
    },
    onDelta: (delta) => {
      process.stdout.write(delta);
    },
    onComplete: (content) => {
      console.log('\n✅ Response completed');
    },
    onError: (error) => {
      console.error(`❌ Error: ${error}`);
    }
  };

  try {
    // Test 1: Basic enhanced execution
    console.log('\n📝 Test 1: Basic Enhanced Execution');
    const input1 = await createInputItem('What is 2+2? Please explain briefly.');
    
    const results1 = await agentLoop.executeLoop(input1, callbacks);
    console.log(`\n📊 Results: ${results1.length} items in transcript`);

    // Test 2: Performance metrics
    console.log('\n📊 Test 2: Performance Metrics');
    const metrics = agentLoop.getPerformanceMetrics();
    console.log('Final Performance Metrics:', {
      totalTime: `${metrics.totalExecutionTime}ms`,
      avgIteration: `${metrics.averageIterationTime}ms`,
      tokens: metrics.tokenUsage,
      tools: metrics.toolCallCount,
      errors: metrics.errorCount,
      success: `${(metrics.successRate * 100).toFixed(1)}%`
    });

    // Test 3: Session management
    console.log('\n💾 Test 3: Session Management');
    const sessionId = agentLoop.getSessionId();
    console.log(`Current session: ${sessionId}`);
    console.log(`Transcript length: ${agentLoop.getTranscript().length}`);

    // Test 4: Configuration update
    console.log('\n⚙️  Test 4: Configuration Update');
    agentLoop.updateConfig({
      enablePerformanceMonitoring: true,
      maxContextTokens: 2000
    });
    console.log('Configuration updated successfully');

    // Test 5: State and capabilities
    console.log('\n🔍 Test 5: Current Capabilities');
    console.log(`State: ${agentLoop.getCurrentState()}`);
    console.log(`Session ID: ${agentLoop.getSessionId()}`);
    console.log(`Thinking time: ${agentLoop.getCumulativeThinkingTime()}ms`);

    console.log('\n🎉 All enhanced features demonstrated successfully!');

  } catch (error) {
    console.error('❌ Demonstration failed:', error.message);
  }
}

// Run demonstration if API key is available
if (process.env.DEEPSEEK_API_KEY) {
  demonstrateEnhancedFeatures().catch(console.error);
} else {
  console.log('⚠️  Set DEEPSEEK_API_KEY environment variable to run the demonstration');
  console.log('Example: DEEPSEEK_API_KEY="your-key" node demo-enhanced-agent.js');
}
