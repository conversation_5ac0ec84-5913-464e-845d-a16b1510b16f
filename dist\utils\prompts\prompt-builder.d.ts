import type { SystemPromptTemplate, SystemPromptConfig, ProviderName } from '../../types/index.js';
export declare class PromptBuilder {
    private template;
    private context;
    private config;
    private providerOptimization;
    private sections;
    private variables;
    constructor(template: SystemPromptTemplate);
    withContext(context: Record<string, any>): PromptBuilder;
    withConfig(config: SystemPromptConfig): PromptBuilder;
    withProviderOptimization(provider: ProviderName): PromptBuilder;
    addSection(title: string, content: string): PromptBuilder;
    withVariables(variables: Record<string, any>): PromptBuilder;
    build(): Promise<string>;
    private prepareVariables;
    private flattenObject;
    private substituteVariables;
    private getNestedValue;
    private applyProviderOptimizations;
    private applyConfigModifications;
    private getSafetyInstructions;
    private getVerbosityInstructions;
    private finalizePrompt;
}
export declare function buildPrompt(template: SystemPromptTemplate, context?: Record<string, any>, config?: SystemPromptConfig, provider?: ProviderName): Promise<string>;
//# sourceMappingURL=prompt-builder.d.ts.map