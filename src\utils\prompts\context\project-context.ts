/**
 * Project Context Provider
 * 
 * Provides project-specific context including package information,
 * dependencies, structure, and configuration details.
 */

import { readFileSync, existsSync, readdirSync, statSync } from 'fs';
import { join, basename, extname } from 'path';
import { execSync } from 'child_process';
import { logInfo, logError } from '../../logger/log.js';

/**
 * Project context interface
 */
export interface ProjectContext {
  name: string;
  type: string;
  language: string;
  framework?: string;
  version?: string;
  description?: string;
  structure: {
    directories: string[];
    files: string[];
    totalFiles: number;
    totalDirectories: number;
  };
  dependencies: {
    production: Record<string, string>;
    development: Record<string, string>;
    total: number;
  };
  scripts: Record<string, string>;
  configuration: {
    hasTypeScript: boolean;
    hasESLint: boolean;
    hasPrettier: boolean;
    hasJest: boolean;
    hasVitest: boolean;
    hasWebpack: boolean;
    hasVite: boolean;
    hasDocker: boolean;
    hasGit: boolean;
  };
  git?: {
    branch: string;
    remotes: string[];
    status: string;
    lastCommit?: string;
  };
  build: {
    outputDir?: string;
    entryPoint?: string;
    buildTool?: string;
  };
}

/**
 * Get comprehensive project context
 */
export async function getProjectContext(projectPath: string = process.cwd()): Promise<ProjectContext> {
  try {
    logInfo('Gathering project context', { projectPath });

    const context: ProjectContext = {
      name: basename(projectPath),
      type: 'unknown',
      language: 'unknown',
      structure: await getProjectStructure(projectPath),
      dependencies: { production: {}, development: {}, total: 0 },
      scripts: {},
      configuration: await getProjectConfiguration(projectPath),
      build: {}
    };

    // Detect project type and get package info
    await detectProjectType(projectPath, context);

    // Get Git information if available
    if (context.configuration.hasGit) {
      context.git = await getGitInfo(projectPath);
    }

    logInfo('Project context gathered successfully', { 
      name: context.name,
      type: context.type,
      language: context.language 
    });

    return context;

  } catch (error) {
    logError('Failed to gather project context', error instanceof Error ? error : new Error(String(error)));
    
    // Return minimal context as fallback
    return getMinimalProjectContext(projectPath);
  }
}

/**
 * Get project structure information
 */
async function getProjectStructure(projectPath: string) {
  const structure = {
    directories: [] as string[],
    files: [] as string[],
    totalFiles: 0,
    totalDirectories: 0
  };

  try {
    const items = readdirSync(projectPath);
    
    for (const item of items) {
      // Skip hidden files and common ignore patterns
      if (item.startsWith('.') && !['package.json', 'tsconfig.json'].includes(item)) {
        continue;
      }
      
      if (['node_modules', 'dist', 'build', '.git'].includes(item)) {
        continue;
      }

      const itemPath = join(projectPath, item);
      const stats = statSync(itemPath);

      if (stats.isDirectory()) {
        structure.directories.push(item);
        structure.totalDirectories++;
      } else {
        structure.files.push(item);
        structure.totalFiles++;
      }
    }

    // Sort for consistency
    structure.directories.sort();
    structure.files.sort();

  } catch (error) {
    logError('Failed to read project structure', error instanceof Error ? error : new Error(String(error)));
  }

  return structure;
}

/**
 * Get project configuration information
 */
async function getProjectConfiguration(projectPath: string) {
  const config = {
    hasTypeScript: false,
    hasESLint: false,
    hasPrettier: false,
    hasJest: false,
    hasVitest: false,
    hasWebpack: false,
    hasVite: false,
    hasDocker: false,
    hasGit: false
  };

  // Check for configuration files
  const configFiles = {
    hasTypeScript: ['tsconfig.json', 'tsconfig.build.json'],
    hasESLint: ['.eslintrc', '.eslintrc.js', '.eslintrc.json', 'eslint.config.js'],
    hasPrettier: ['.prettierrc', '.prettierrc.js', '.prettierrc.json', 'prettier.config.js'],
    hasJest: ['jest.config.js', 'jest.config.json', 'jest.config.ts'],
    hasVitest: ['vitest.config.js', 'vitest.config.ts', 'vite.config.js', 'vite.config.ts'],
    hasWebpack: ['webpack.config.js', 'webpack.config.ts'],
    hasVite: ['vite.config.js', 'vite.config.ts'],
    hasDocker: ['Dockerfile', 'docker-compose.yml', 'docker-compose.yaml'],
    hasGit: ['.git']
  };

  for (const [key, files] of Object.entries(configFiles)) {
    for (const file of files) {
      if (existsSync(join(projectPath, file))) {
        (config as any)[key] = true;
        break;
      }
    }
  }

  return config;
}

/**
 * Detect project type and extract package information
 */
async function detectProjectType(projectPath: string, context: ProjectContext) {
  // Check for package.json (Node.js/JavaScript project)
  const packageJsonPath = join(projectPath, 'package.json');
  if (existsSync(packageJsonPath)) {
    await parsePackageJson(packageJsonPath, context);
    return;
  }

  // Check for Python projects
  const pythonFiles = ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'];
  for (const file of pythonFiles) {
    if (existsSync(join(projectPath, file))) {
      context.type = 'python';
      context.language = 'python';
      await parsePythonProject(projectPath, context);
      return;
    }
  }

  // Check for Rust projects
  if (existsSync(join(projectPath, 'Cargo.toml'))) {
    context.type = 'rust';
    context.language = 'rust';
    await parseCargoToml(projectPath, context);
    return;
  }

  // Check for Go projects
  if (existsSync(join(projectPath, 'go.mod'))) {
    context.type = 'go';
    context.language = 'go';
    await parseGoMod(projectPath, context);
    return;
  }

  // Check for Java projects
  const javaFiles = ['pom.xml', 'build.gradle', 'build.gradle.kts'];
  for (const file of javaFiles) {
    if (existsSync(join(projectPath, file))) {
      context.type = 'java';
      context.language = 'java';
      context.framework = file.includes('gradle') ? 'gradle' : 'maven';
      return;
    }
  }

  // Fallback: detect by file extensions
  const files = context.structure.files;
  const extensions = files.map(f => extname(f).toLowerCase());
  
  if (extensions.includes('.ts') || extensions.includes('.tsx')) {
    context.language = 'typescript';
    context.type = 'typescript';
  } else if (extensions.includes('.js') || extensions.includes('.jsx')) {
    context.language = 'javascript';
    context.type = 'javascript';
  } else if (extensions.includes('.py')) {
    context.language = 'python';
    context.type = 'python';
  } else if (extensions.includes('.rs')) {
    context.language = 'rust';
    context.type = 'rust';
  } else if (extensions.includes('.go')) {
    context.language = 'go';
    context.type = 'go';
  }
}

/**
 * Parse package.json for Node.js projects
 */
async function parsePackageJson(packageJsonPath: string, context: ProjectContext) {
  try {
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
    
    context.name = packageJson.name || context.name;
    context.version = packageJson.version;
    context.description = packageJson.description;
    context.type = 'nodejs';
    
    // Detect language
    if (context.configuration.hasTypeScript) {
      context.language = 'typescript';
    } else {
      context.language = 'javascript';
    }

    // Detect framework
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    if (deps.react) context.framework = 'react';
    else if (deps.vue) context.framework = 'vue';
    else if (deps.angular) context.framework = 'angular';
    else if (deps.express) context.framework = 'express';
    else if (deps.next) context.framework = 'nextjs';
    else if (deps.nuxt) context.framework = 'nuxtjs';
    else if (deps.svelte) context.framework = 'svelte';

    // Extract dependencies
    context.dependencies.production = packageJson.dependencies || {};
    context.dependencies.development = packageJson.devDependencies || {};
    context.dependencies.total = Object.keys(context.dependencies.production).length + 
                                Object.keys(context.dependencies.development).length;

    // Extract scripts
    context.scripts = packageJson.scripts || {};

    // Extract build configuration
    context.build.entryPoint = packageJson.main || 'index.js';
    if (packageJson.scripts?.build) {
      if (packageJson.scripts.build.includes('webpack')) {
        context.build.buildTool = 'webpack';
      } else if (packageJson.scripts.build.includes('vite')) {
        context.build.buildTool = 'vite';
      } else if (packageJson.scripts.build.includes('tsc')) {
        context.build.buildTool = 'typescript';
      }
    }

  } catch (error) {
    logError('Failed to parse package.json', error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Parse Python project information
 */
async function parsePythonProject(projectPath: string, context: ProjectContext) {
  // Try to read requirements.txt
  const reqPath = join(projectPath, 'requirements.txt');
  if (existsSync(reqPath)) {
    try {
      const requirements = readFileSync(reqPath, 'utf8')
        .split('\n')
        .filter(line => line.trim() && !line.startsWith('#'))
        .reduce((deps, line) => {
          const [name] = line.split(/[>=<]/);
          deps[name.trim()] = line.trim();
          return deps;
        }, {} as Record<string, string>);
      
      context.dependencies.production = requirements;
      context.dependencies.total = Object.keys(requirements).length;
    } catch (error) {
      logError('Failed to parse requirements.txt', error instanceof Error ? error : new Error(String(error)));
    }
  }
}

/**
 * Parse Cargo.toml for Rust projects
 */
async function parseCargoToml(projectPath: string, context: ProjectContext) {
  // Basic parsing - would need a proper TOML parser for full support
  const cargoPath = join(projectPath, 'Cargo.toml');
  try {
    const cargoContent = readFileSync(cargoPath, 'utf8');
    const nameMatch = cargoContent.match(/name\s*=\s*"([^"]+)"/);
    const versionMatch = cargoContent.match(/version\s*=\s*"([^"]+)"/);
    
    if (nameMatch) context.name = nameMatch[1];
    if (versionMatch) context.version = versionMatch[1];
  } catch (error) {
    logError('Failed to parse Cargo.toml', error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Parse go.mod for Go projects
 */
async function parseGoMod(projectPath: string, context: ProjectContext) {
  const goModPath = join(projectPath, 'go.mod');
  try {
    const goModContent = readFileSync(goModPath, 'utf8');
    const moduleMatch = goModContent.match(/module\s+(.+)/);
    
    if (moduleMatch) {
      context.name = moduleMatch[1].split('/').pop() || context.name;
    }
  } catch (error) {
    logError('Failed to parse go.mod', error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Get Git information
 */
async function getGitInfo(projectPath: string) {
  const git = {
    branch: 'unknown',
    remotes: [] as string[],
    status: 'unknown',
    lastCommit: undefined as string | undefined
  };

  try {
    // Get current branch
    git.branch = execSync('git branch --show-current', { 
      cwd: projectPath, 
      encoding: 'utf8' 
    }).trim();

    // Get remotes
    const remotes = execSync('git remote -v', { 
      cwd: projectPath, 
      encoding: 'utf8' 
    }).trim();
    git.remotes = remotes.split('\n').map(line => line.split('\t')[0]).filter(Boolean);

    // Get status
    git.status = execSync('git status --porcelain', { 
      cwd: projectPath, 
      encoding: 'utf8' 
    }).trim() ? 'dirty' : 'clean';

    // Get last commit
    git.lastCommit = execSync('git log -1 --format="%h %s"', { 
      cwd: projectPath, 
      encoding: 'utf8' 
    }).trim();

  } catch (error) {
    logError('Failed to get Git info', error instanceof Error ? error : new Error(String(error)));
  }

  return git;
}

/**
 * Get minimal project context as fallback
 */
function getMinimalProjectContext(projectPath: string): ProjectContext {
  return {
    name: basename(projectPath),
    type: 'unknown',
    language: 'unknown',
    structure: {
      directories: [],
      files: [],
      totalFiles: 0,
      totalDirectories: 0
    },
    dependencies: {
      production: {},
      development: {},
      total: 0
    },
    scripts: {},
    configuration: {
      hasTypeScript: false,
      hasESLint: false,
      hasPrettier: false,
      hasJest: false,
      hasVitest: false,
      hasWebpack: false,
      hasVite: false,
      hasDocker: false,
      hasGit: false
    },
    build: {}
  };
}
