import { execSync } from 'child_process';
import { platform } from 'os';
import { logInfo, logError } from '../../logger/log.js';
export async function getToolContext(config) {
    try {
        logInfo('Gathering tool context');
        const context = {
            available: getAvailableTools(),
            permissions: getPermissions(config),
            restrictions: getRestrictions(config),
            capabilities: await getCapabilities(),
            functions: getFunctionInfo(),
            environment: getEnvironmentInfo(),
            limits: getLimits(config)
        };
        logInfo('Tool context gathered successfully', {
            availableTools: context.available.length,
            capabilities: Object.keys(context.capabilities).filter(k => context.capabilities[k]).length
        });
        return context;
    }
    catch (error) {
        logError('Failed to gather tool context', error instanceof Error ? error : new Error(String(error)));
        return getMinimalToolContext(config);
    }
}
function getAvailableTools() {
    return [
        'shell_command',
        'file_read',
        'file_write',
        'file_list',
        'file_search',
        'git_operations',
        'package_management',
        'system_info',
        'process_management',
        'network_operations',
        'text_processing',
        'code_analysis',
        'documentation_generation',
        'testing_utilities',
        'debugging_tools'
    ];
}
function getPermissions(config) {
    const approvalMode = config.approvalMode || 'suggest';
    return {
        canExecuteCommands: true,
        canWriteFiles: true,
        canReadFiles: true,
        canInstallPackages: approvalMode !== 'suggest',
        canModifySystem: approvalMode === 'full-auto',
        approvalRequired: approvalMode === 'suggest'
            ? ['system_commands', 'file_modifications', 'package_installation']
            : approvalMode === 'auto-edit'
                ? ['system_commands', 'package_installation']
                : []
    };
}
function getRestrictions(config) {
    return {
        safeCommands: config.safeCommands || [
            'ls', 'cat', 'grep', 'find', 'head', 'tail', 'wc', 'echo', 'pwd', 'which',
            'git status', 'git log', 'git diff', 'npm list', 'node --version'
        ],
        dangerousCommands: config.dangerousCommands || [
            'rm', 'sudo', 'chmod', 'chown', 'dd', 'mkfs', 'fdisk', 'format',
            'del', 'rmdir', 'takeown', 'icacls'
        ],
        blockedPaths: [
            '/etc/passwd', '/etc/shadow', '/etc/sudoers',
            'C:\\Windows\\System32', 'C:\\Windows\\SysWOW64',
            '/System', '/usr/bin', '/sbin'
        ],
        timeoutLimits: {
            shell_command: config.timeout || 30000,
            file_operations: 10000,
            network_operations: 15000,
            package_installation: 120000
        }
    };
}
async function getCapabilities() {
    const capabilities = {
        shellAccess: true,
        fileSystem: true,
        networkAccess: false,
        packageManagement: false,
        gitOperations: false,
        dockerAccess: false,
        systemInfo: true
    };
    try {
        try {
            execSync('git --version', { stdio: 'ignore', timeout: 5000 });
            capabilities.gitOperations = true;
        }
        catch {
        }
        const packageManagers = platform() === 'win32'
            ? ['npm', 'yarn', 'choco', 'winget']
            : ['npm', 'yarn', 'apt', 'yum', 'brew', 'pip'];
        for (const pm of packageManagers) {
            try {
                execSync(`${pm} --version`, { stdio: 'ignore', timeout: 3000 });
                capabilities.packageManagement = true;
                break;
            }
            catch {
            }
        }
        try {
            execSync('docker --version', { stdio: 'ignore', timeout: 5000 });
            capabilities.dockerAccess = true;
        }
        catch {
        }
        try {
            execSync(platform() === 'win32' ? 'ping -n 1 *******' : 'ping -c 1 *******', {
                stdio: 'ignore',
                timeout: 5000
            });
            capabilities.networkAccess = true;
        }
        catch {
        }
    }
    catch (error) {
        logError('Failed to check capabilities', error instanceof Error ? error : new Error(String(error)));
    }
    return capabilities;
}
function getFunctionInfo() {
    const builtin = [
        {
            name: 'shell_command',
            description: 'Execute shell commands',
            category: 'system',
            parameters: ['command', 'workdir', 'timeout'],
            examples: ['ls -la', 'git status', 'npm install'],
            restrictions: ['Dangerous commands require approval']
        },
        {
            name: 'file_read',
            description: 'Read file contents',
            category: 'filesystem',
            parameters: ['path', 'encoding'],
            examples: ['package.json', 'src/index.ts'],
            restrictions: ['Cannot read system files']
        },
        {
            name: 'file_write',
            description: 'Write or modify files',
            category: 'filesystem',
            parameters: ['path', 'content', 'mode'],
            examples: ['Create new file', 'Update existing file'],
            restrictions: ['Cannot write to system directories']
        },
        {
            name: 'file_search',
            description: 'Search for files and content',
            category: 'filesystem',
            parameters: ['pattern', 'directory', 'type'],
            examples: ['Find .js files', 'Search for text in files'],
            restrictions: ['Limited to project directories']
        },
        {
            name: 'git_operations',
            description: 'Git version control operations',
            category: 'development',
            parameters: ['operation', 'arguments'],
            examples: ['git status', 'git add .', 'git commit'],
            restrictions: ['Push operations require approval']
        },
        {
            name: 'package_management',
            description: 'Install and manage packages',
            category: 'development',
            parameters: ['manager', 'operation', 'packages'],
            examples: ['npm install', 'pip install', 'apt update'],
            restrictions: ['Installation requires approval']
        },
        {
            name: 'system_info',
            description: 'Get system information',
            category: 'system',
            parameters: ['type'],
            examples: ['OS info', 'Hardware specs', 'Process list'],
            restrictions: ['No sensitive information']
        }
    ];
    const custom = [];
    return {
        builtin,
        custom,
        total: builtin.length + custom.length
    };
}
function getEnvironmentInfo() {
    const shell = platform() === 'win32'
        ? process.env.COMSPEC || 'cmd.exe'
        : process.env.SHELL || '/bin/bash';
    return {
        shell: shell.split(/[/\\]/).pop() || 'unknown',
        workingDirectory: process.cwd(),
        pathSeparator: platform() === 'win32' ? '\\' : '/',
        environmentVariables: Object.keys(process.env).filter(key => !['PASSWORD', 'SECRET', 'KEY', 'TOKEN', 'API_KEY', 'AUTH'].some(sensitive => key.toUpperCase().includes(sensitive)))
    };
}
function getLimits(config) {
    return {
        maxExecutionTime: config.timeout || 30000,
        maxFileSize: 10 * 1024 * 1024,
        maxOutputLength: 100 * 1024,
        concurrentOperations: 3
    };
}
function getMinimalToolContext(config) {
    return {
        available: ['shell_command', 'file_read', 'file_write', 'system_info'],
        permissions: {
            canExecuteCommands: true,
            canWriteFiles: true,
            canReadFiles: true,
            canInstallPackages: false,
            canModifySystem: false,
            approvalRequired: ['system_commands']
        },
        restrictions: {
            safeCommands: ['ls', 'cat', 'echo', 'pwd'],
            dangerousCommands: ['rm', 'sudo', 'chmod'],
            blockedPaths: ['/etc', '/sys', '/proc'],
            timeoutLimits: {
                shell_command: 30000,
                file_operations: 10000
            }
        },
        capabilities: {
            shellAccess: true,
            fileSystem: true,
            networkAccess: false,
            packageManagement: false,
            gitOperations: false,
            dockerAccess: false,
            systemInfo: true
        },
        functions: {
            builtin: [],
            custom: [],
            total: 0
        },
        environment: {
            shell: 'unknown',
            workingDirectory: process.cwd(),
            pathSeparator: platform() === 'win32' ? '\\' : '/',
            environmentVariables: []
        },
        limits: {
            maxExecutionTime: 30000,
            maxFileSize: 1024 * 1024,
            maxOutputLength: 10 * 1024,
            concurrentOperations: 1
        }
    };
}
export function isCommandSafe(command, config) {
    if (!command || command.length === 0)
        return false;
    const cmd = command[0].toLowerCase();
    const safeCommands = config.safeCommands || [];
    const dangerousCommands = config.dangerousCommands || [];
    if (safeCommands.includes(cmd))
        return true;
    if (dangerousCommands.includes(cmd))
        return false;
    return false;
}
export function getToolUsageStats() {
    return {
        mostUsedTools: [],
        totalExecutions: 0,
        successRate: 0,
        averageExecutionTime: 0
    };
}
