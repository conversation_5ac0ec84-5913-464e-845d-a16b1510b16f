/**
 * Session Context Provider
 * 
 * Provides session-specific context including conversation history,
 * user preferences, session metadata, and interaction patterns.
 */

import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { logInfo, logError } from '../../logger/log.js';
import { loadConfig } from '../../config.js';
import { getSessionsDir } from '../../storage/sessions.js';
import { loadHistory } from '../../storage/command-history.js';
import type { SessionMetadata, HistoryEntry } from '../../../types/index.js';

/**
 * Session context interface
 */
export interface SessionContext {
  id: string;
  startTime: number;
  duration: number;
  messageCount: number;
  lastActivity: number;
  preferences: {
    model: string;
    provider: string;
    approvalMode: string;
    verbosity: string;
    theme: string;
  };
  history: {
    commands: HistoryEntry[];
    recentCommands: string[];
    frequentCommands: string[];
    commandCount: number;
  };
  interaction: {
    totalSessions: number;
    averageSessionLength: number;
    preferredCommands: string[];
    errorRate: number;
    successRate: number;
  };
  current: {
    workingDirectory: string;
    activeFiles: string[];
    recentFiles: string[];
    openProjects: string[];
  };
  user: {
    expertise: string[];
    preferences: Record<string, any>;
    customInstructions?: string;
    learningMode: boolean;
  };
}

/**
 * Global session state
 */
let currentSessionId: string | null = null;
let sessionStartTime: number = Date.now();
let messageCount: number = 0;

/**
 * Get comprehensive session context
 */
export async function getSessionContext(): Promise<SessionContext> {
  try {
    logInfo('Gathering session context');

    const config = loadConfig();
    const sessionId = getCurrentSessionId();
    
    const context: SessionContext = {
      id: sessionId,
      startTime: sessionStartTime,
      duration: Date.now() - sessionStartTime,
      messageCount: messageCount,
      lastActivity: Date.now(),
      preferences: {
        model: config.model,
        provider: config.provider,
        approvalMode: config.approvalMode,
        verbosity: 'normal',
        theme: 'default'
      },
      history: await getHistoryContext(),
      interaction: await getInteractionContext(),
      current: await getCurrentContext(),
      user: await getUserContext()
    };

    logInfo('Session context gathered successfully', { 
      sessionId: context.id,
      duration: context.duration,
      messageCount: context.messageCount 
    });

    return context;

  } catch (error) {
    logError('Failed to gather session context', error instanceof Error ? error : new Error(String(error)));
    
    // Return minimal context as fallback
    return getMinimalSessionContext();
  }
}

/**
 * Get or generate current session ID
 */
function getCurrentSessionId(): string {
  if (!currentSessionId) {
    currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  return currentSessionId;
}

/**
 * Update session message count
 */
export function incrementMessageCount(): void {
  messageCount++;
}

/**
 * Reset session (for new session)
 */
export function resetSession(): void {
  currentSessionId = null;
  sessionStartTime = Date.now();
  messageCount = 0;
}

/**
 * Get command history context
 */
async function getHistoryContext() {
  const history = {
    commands: [] as HistoryEntry[],
    recentCommands: [] as string[],
    frequentCommands: [] as string[],
    commandCount: 0
  };

  try {
    const commandHistory = loadHistory();
    history.commands = commandHistory;
    history.commandCount = commandHistory.length;

    // Get recent commands (last 10)
    history.recentCommands = commandHistory
      .slice(-10)
      .map(entry => entry.command);

    // Get frequent commands
    const commandCounts = new Map<string, number>();
    commandHistory.forEach(entry => {
      const count = commandCounts.get(entry.command) || 0;
      commandCounts.set(entry.command, count + 1);
    });

    history.frequentCommands = Array.from(commandCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([command]) => command);

  } catch (error) {
    logError('Failed to load command history', error instanceof Error ? error : new Error(String(error)));
  }

  return history;
}

/**
 * Get interaction patterns context
 */
async function getInteractionContext() {
  const interaction = {
    totalSessions: 0,
    averageSessionLength: 0,
    preferredCommands: [] as string[],
    errorRate: 0,
    successRate: 0
  };

  try {
    const sessionsDir = getSessionsDir();
    
    if (existsSync(sessionsDir)) {
      // Count session files
      const { readdirSync } = await import('fs');
      const sessionFiles = readdirSync(sessionsDir).filter(f => f.endsWith('.json'));
      interaction.totalSessions = sessionFiles.length;

      // Analyze recent sessions for patterns
      const recentSessions = sessionFiles
        .slice(-10) // Last 10 sessions
        .map(file => {
          try {
            const sessionPath = join(sessionsDir, file);
            const sessionData = JSON.parse(readFileSync(sessionPath, 'utf8'));
            return sessionData;
          } catch {
            return null;
          }
        })
        .filter(Boolean);

      if (recentSessions.length > 0) {
        // Calculate average session length
        const totalDuration = recentSessions.reduce((sum, session) => {
          const start = session.metadata?.timestamp || 0;
          const end = session.metadata?.lastActivity || start;
          return sum + (end - start);
        }, 0);
        interaction.averageSessionLength = totalDuration / recentSessions.length;

        // Analyze success/error rates from command history
        const commandHistory = loadHistory();
        const recentCommands = commandHistory.slice(-50); // Last 50 commands
        
        if (recentCommands.length > 0) {
          const successfulCommands = recentCommands.filter(cmd => cmd.success !== false).length;
          interaction.successRate = successfulCommands / recentCommands.length;
          interaction.errorRate = 1 - interaction.successRate;
        }
      }
    }

  } catch (error) {
    logError('Failed to analyze interaction patterns', error instanceof Error ? error : new Error(String(error)));
  }

  return interaction;
}

/**
 * Get current session context
 */
async function getCurrentContext() {
  const current = {
    workingDirectory: process.cwd(),
    activeFiles: [] as string[],
    recentFiles: [] as string[],
    openProjects: [] as string[]
  };

  try {
    // Get recently accessed files from command history
    const commandHistory = loadHistory();
    const fileCommands = commandHistory
      .filter(entry => {
        const cmd = entry.command.toLowerCase();
        return cmd.includes('cat') || cmd.includes('edit') || cmd.includes('open') || 
               cmd.includes('code') || cmd.includes('vim') || cmd.includes('nano');
      })
      .slice(-20); // Last 20 file-related commands

    // Extract file paths from commands (basic extraction)
    const filePattern = /(?:cat|edit|open|code|vim|nano)\s+([^\s]+)/gi;
    const recentFiles = new Set<string>();

    fileCommands.forEach(entry => {
      let match;
      while ((match = filePattern.exec(entry.command)) !== null) {
        const filePath = match[1];
        if (filePath && !filePath.startsWith('-')) {
          recentFiles.add(filePath);
        }
      }
    });

    current.recentFiles = Array.from(recentFiles).slice(0, 10);

    // Detect open projects (directories with package.json, etc.)
    current.openProjects = [process.cwd()]; // Current directory as default

  } catch (error) {
    logError('Failed to get current context', error instanceof Error ? error : new Error(String(error)));
  }

  return current;
}

/**
 * Get user context and preferences
 */
async function getUserContext() {
  const user = {
    expertise: [] as string[],
    preferences: {} as Record<string, any>,
    customInstructions: undefined as string | undefined,
    learningMode: false
  };

  try {
    const config = loadConfig();
    
    // Extract user preferences from config
    user.preferences = {
      notifications: config.enableNotifications,
      logging: config.enableLogging,
      safetyLevel: 'moderate', // Default
      autoApproval: config.approvalMode === 'full-auto'
    };

    // Infer expertise from command history and project types
    const commandHistory = loadHistory();
    const commands = commandHistory.map(entry => entry.command.toLowerCase());
    
    // Detect expertise areas based on command patterns
    const expertisePatterns = {
      'git': /git\s+/,
      'docker': /docker\s+/,
      'kubernetes': /kubectl\s+/,
      'nodejs': /npm\s+|node\s+|yarn\s+/,
      'python': /python\s+|pip\s+|conda\s+/,
      'rust': /cargo\s+|rustc\s+/,
      'go': /go\s+(run|build|test)/,
      'database': /mysql\s+|psql\s+|mongo\s+/,
      'aws': /aws\s+/,
      'linux': /sudo\s+|systemctl\s+|grep\s+|awk\s+/
    };

    for (const [area, pattern] of Object.entries(expertisePatterns)) {
      const matchCount = commands.filter(cmd => pattern.test(cmd)).length;
      if (matchCount > 5) { // Threshold for expertise
        user.expertise.push(area);
      }
    }

    // Check for custom instructions in config
    if (config.projectDocPath && existsSync(config.projectDocPath)) {
      try {
        user.customInstructions = readFileSync(config.projectDocPath, 'utf8');
      } catch {
        // Ignore error
      }
    }

    // Determine learning mode based on interaction patterns
    user.learningMode = commandHistory.length < 50; // New user

  } catch (error) {
    logError('Failed to get user context', error instanceof Error ? error : new Error(String(error)));
  }

  return user;
}

/**
 * Get minimal session context as fallback
 */
function getMinimalSessionContext(): SessionContext {
  const sessionId = getCurrentSessionId();
  
  return {
    id: sessionId,
    startTime: sessionStartTime,
    duration: Date.now() - sessionStartTime,
    messageCount: messageCount,
    lastActivity: Date.now(),
    preferences: {
      model: 'gpt-4',
      provider: 'openai',
      approvalMode: 'suggest',
      verbosity: 'normal',
      theme: 'default'
    },
    history: {
      commands: [],
      recentCommands: [],
      frequentCommands: [],
      commandCount: 0
    },
    interaction: {
      totalSessions: 0,
      averageSessionLength: 0,
      preferredCommands: [],
      errorRate: 0,
      successRate: 0
    },
    current: {
      workingDirectory: process.cwd(),
      activeFiles: [],
      recentFiles: [],
      openProjects: []
    },
    user: {
      expertise: [],
      preferences: {},
      learningMode: true
    }
  };
}

/**
 * Update session activity timestamp
 */
export function updateLastActivity(): void {
  // This would typically update a persistent session record
  // For now, we just track in memory
}

/**
 * Get session statistics
 */
export function getSessionStats() {
  return {
    id: getCurrentSessionId(),
    startTime: sessionStartTime,
    duration: Date.now() - sessionStartTime,
    messageCount: messageCount
  };
}
