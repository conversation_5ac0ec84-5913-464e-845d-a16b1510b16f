/**
 * Input Utilities for Multi-Modal AI Interaction
 * 
 * Handles text and image input processing for AI conversations
 * Supports various image formats and automatic optimization
 */

import { readFileSync, existsSync, statSync } from 'fs';
import { extname } from 'path';
import type { ResponseInputItem, ResponseContentInput, MessageContent } from '../types/index.js';

// Supported image formats
const SUPPORTED_IMAGE_FORMATS = ['.png', '.jpg', '.jpeg', '.webp', '.gif'];

// Maximum image size (5MB)
const MAX_IMAGE_SIZE = 5 * 1024 * 1024;

/**
 * Create input item from text and image paths or MessageContent
 */
export async function createInputItem(
  text: string,
  contentOrImagePaths: MessageContent[] | string[] = []
): Promise<ResponseInputItem> {
  const content: ResponseContentInput[] = [];

  // Add text content if provided
  if (text && text.trim()) {
    const sanitizedText = sanitizeTextInput(text);
    if (sanitizedText) {
      content.push({
        type: 'input_text',
        text: sanitizedText
      });
    }
  }

  // Handle different content types
  if (Array.isArray(contentOrImagePaths)) {
    if (contentOrImagePaths.length > 0) {
      // Check if it's MessageContent[] or string[]
      const firstItem = contentOrImagePaths[0];

      if (typeof firstItem === 'string') {
        // Handle as image paths (legacy)
        const imagePaths = contentOrImagePaths as string[];
        for (const imagePath of imagePaths) {
          await processImagePath(imagePath, content);
        }
      } else {
        // Handle as MessageContent[]
        const messageContent = contentOrImagePaths as MessageContent[];
        for (const item of messageContent) {
          if (item.type === 'input_text') {
            content.push({
              type: 'input_text',
              text: item.text
            });
          } else if (item.type === 'image') {
            content.push({
              type: 'image',
              // source: item.source // Removed for compatibility
            });
          }
        }
      }
    }
  }

  const inputItem: ResponseInputItem = {
    type: 'input',
    role: 'user',
    content,
    timestamp: Date.now()
  };

  // Validate the created input item
  const validation = validateInputItem(inputItem);
  if (!validation.valid) {
    throw new Error(`Invalid input item: ${validation.errors.join(', ')}`);
  }

  return inputItem;
}

/**
 * Process image path and add to content
 */
async function processImagePath(imagePath: string, content: ResponseContentInput[]): Promise<void> {
  try {
    const imageData = await processImage(imagePath);
    content.push({
      type: 'input_image',
      image: imageData
    });
  } catch (error) {
    console.warn(`Warning: Could not process image ${imagePath}:`, error);
    // Add error message to text content
    content.push({
      type: 'input_text',
      text: `[Error: Could not load image ${imagePath}]`
    });
  }
}

/**
 * Process image file for AI consumption
 */
async function processImage(imagePath: string): Promise<{
  url: string;
  detail?: 'low' | 'high' | 'auto';
}> {
  // Validate file exists
  if (!existsSync(imagePath)) {
    throw new Error(`Image file not found: ${imagePath}`);
  }

  // Validate file format
  const ext = extname(imagePath).toLowerCase();
  if (!SUPPORTED_IMAGE_FORMATS.includes(ext)) {
    throw new Error(`Unsupported image format: ${ext}. Supported formats: ${SUPPORTED_IMAGE_FORMATS.join(', ')}`);
  }

  // Check file size
  const stats = statSync(imagePath);
  if (stats.size > MAX_IMAGE_SIZE) {
    throw new Error(`Image file too large: ${stats.size} bytes. Maximum size: ${MAX_IMAGE_SIZE} bytes`);
  }

  // Read and encode image
  const imageBuffer = readFileSync(imagePath);
  const base64Data = imageBuffer.toString('base64');
  
  // Determine MIME type
  const mimeType = getMimeType(ext);
  const dataUrl = `data:${mimeType};base64,${base64Data}`;

  // Determine detail level based on file size
  let detail: 'low' | 'high' | 'auto' = 'auto';
  if (stats.size < 512 * 1024) { // < 512KB
    detail = 'low';
  } else if (stats.size > 2 * 1024 * 1024) { // > 2MB
    detail = 'high';
  }

  return {
    url: dataUrl,
    detail
  };
}

/**
 * Get MIME type for image extension
 */
function getMimeType(ext: string): string {
  const mimeTypes: Record<string, string> = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.webp': 'image/webp',
    '.gif': 'image/gif'
  };
  
  return mimeTypes[ext.toLowerCase()] || 'image/jpeg';
}

/**
 * Extract image paths from text (e.g., @image.png)
 */
export function extractImagePaths(text: string): {
  cleanText: string;
  imagePaths: string[];
} {
  const imagePattern = /@([^\s]+\.(png|jpg|jpeg|webp|gif))/gi;
  const imagePaths: string[] = [];
  
  const cleanText = text.replace(imagePattern, (match, path) => {
    imagePaths.push(path);
    return `[Image: ${path}]`;
  });

  return { cleanText, imagePaths };
}

/**
 * Validate image paths exist and are accessible
 */
export function validateImagePaths(imagePaths: string[]): {
  valid: string[];
  invalid: string[];
} {
  const valid: string[] = [];
  const invalid: string[] = [];

  for (const path of imagePaths) {
    try {
      if (existsSync(path)) {
        const stats = statSync(path);
        if (stats.isFile()) {
          const ext = extname(path).toLowerCase();
          if (SUPPORTED_IMAGE_FORMATS.includes(ext)) {
            if (stats.size <= MAX_IMAGE_SIZE) {
              valid.push(path);
            } else {
              invalid.push(`${path} (too large: ${stats.size} bytes)`);
            }
          } else {
            invalid.push(`${path} (unsupported format: ${ext})`);
          }
        } else {
          invalid.push(`${path} (not a file)`);
        }
      } else {
        invalid.push(`${path} (not found)`);
      }
    } catch (error) {
      invalid.push(`${path} (access error: ${error})`);
    }
  }

  return { valid, invalid };
}

/**
 * Get image information without loading
 */
export function getImageInfo(imagePath: string): {
  exists: boolean;
  size?: number;
  format?: string;
  supported?: boolean;
} {
  try {
    if (!existsSync(imagePath)) {
      return { exists: false };
    }

    const stats = statSync(imagePath);
    const ext = extname(imagePath).toLowerCase();
    const supported = SUPPORTED_IMAGE_FORMATS.includes(ext);

    return {
      exists: true,
      size: stats.size,
      format: ext,
      supported
    };
  } catch (error) {
    return { exists: false };
  }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Create system message for context
 */
export function createSystemMessage(content: string): ResponseInputItem {
  return {
    role: 'system',
    content: [{
      type: 'input_text',
      text: content
    }],
    type: 'message',
    timestamp: Date.now()
  };
}

/**
 * Create assistant message for conversation history
 */
export function createAssistantMessage(content: string): ResponseInputItem {
  return {
    role: 'assistant',
    content: [{
      type: 'input_text',
      text: content
    }],
    type: 'message',
    timestamp: Date.now()
  };
}

/**
 * Validate input item has valid content
 */
export function validateInputItem(item: ResponseInputItem): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!item.content || !Array.isArray(item.content)) {
    errors.push('Input item must have content array');
  } else if (item.content.length === 0) {
    errors.push('Input item content cannot be empty');
  } else {
    const hasValidContent = item.content.some(c => {
      if (c.type === 'input_text') {
        return c.text && c.text.trim().length > 0;
      } else if (c.type === 'input_image' || c.type === 'image') {
        return c.image && c.image.url;
      }
      return false;
    });

    if (!hasValidContent) {
      errors.push('Input item must contain at least one valid text or image content');
    }
  }

  if (!['user', 'assistant', 'system'].includes(item.role)) {
    errors.push('Input item must have valid role (user, assistant, or system)');
  }

  if (!['input', 'message'].includes(item.type)) {
    errors.push('Input item must have valid type (input or message)');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize and validate text input
 */
export function sanitizeTextInput(text: string): string {
  if (typeof text !== 'string') {
    throw new Error('Input must be a string');
  }

  // Remove null bytes and other control characters except newlines and tabs
  const sanitized = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  // Trim excessive whitespace but preserve intentional formatting
  return sanitized.trim();
}
