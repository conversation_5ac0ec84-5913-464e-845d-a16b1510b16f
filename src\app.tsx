/**
 * Main Application Class
 * 
 * Orchestrates the entire Kritrima AI CLI application
 * Handles git validation, safety checks, and UI initialization
 */

import React from 'react';
import { render } from 'ink';
import chalk from 'chalk';
import { checkInGit } from './utils/check-in-git.js';
import { TerminalChat } from './components/chat/terminal-chat.js';
import { logInfo, logError } from './utils/logger/log.js';
import { initializeTelemetry, trackEvent, trackError, shutdownTelemetry } from './utils/telemetry.js';
import type { AppConfig } from './types/index.js';

export class App {
  private config: AppConfig;
  private inkInstance: any = null;

  constructor(config: AppConfig) {
    this.config = config;

    // Initialize telemetry
    initializeTelemetry({
      enabled: config.telemetryEnabled !== false,
      anonymize: true,
      collectPerformance: true,
      collectErrors: true,
      collectUsage: true
    });
  }

  /**
   * Start the application
   */
  async start(): Promise<void> {
    const startTime = Date.now();

    try {
      logInfo('Starting Kritrima AI CLI application');

      // Track app start
      trackEvent('app_start', {
        provider: this.config.provider,
        model: this.config.model,
        approvalMode: this.config.approvalMode
      });

      // Perform startup checks
      await this.performStartupChecks();

      // Initialize terminal UI
      await this.initializeUI();

      const duration = Date.now() - startTime;
      logInfo('Application started successfully');

      // Track successful startup
      trackEvent('app_start', {
        success: true,
        duration,
        provider: this.config.provider,
        model: this.config.model
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      logError('Failed to start application', error instanceof Error ? error : new Error(String(error)));

      // Track startup failure
      trackError(error instanceof Error ? error : new Error(String(error)), {
        phase: 'startup',
        duration,
        config: this.config
      });

      throw error;
    }
  }

  /**
   * Stop the application
   */
  async stop(): Promise<void> {
    try {
      logInfo('Stopping Kritrima AI CLI application');

      if (this.inkInstance) {
        this.inkInstance.unmount();
        this.inkInstance = null;
      }

      // Shutdown telemetry
      await shutdownTelemetry();

      logInfo('Application stopped successfully');

    } catch (error) {
      logError('Error stopping application', error instanceof Error ? error : new Error(String(error)));
      trackError(error instanceof Error ? error : new Error(String(error)), {
        phase: 'shutdown'
      });
    }
  }

  /**
   * Perform startup checks and validations
   */
  private async performStartupChecks(): Promise<void> {
    // Check git repository status
    const inGit = checkInGit(process.cwd());
    if (!inGit) {
      console.log(chalk.yellow('⚠ Warning: Not in a git repository'));
      console.log(chalk.gray('  Some features like git diff and change tracking will be limited'));
      console.log();
    }

    // Check working directory permissions
    try {
      const { accessSync, constants } = await import('fs');
      accessSync(process.cwd(), constants.R_OK | constants.W_OK);
    } catch (_error) {
      throw new Error(`Working directory is not accessible: ${process.cwd()}`);
    }

    // Validate configuration
    this.validateConfiguration();

    // Check API connectivity (optional)
    await this.checkAPIConnectivity();
  }

  /**
   * Validate application configuration
   */
  private validateConfiguration(): void {
    if (!this.config.model) {
      throw new Error('No model specified in configuration');
    }

    if (!this.config.provider) {
      throw new Error('No provider specified in configuration');
    }

    if (!this.config.approvalMode) {
      throw new Error('No approval mode specified in configuration');
    }

    // Validate approval mode
    const validModes = ['suggest', 'auto-edit', 'full-auto'];
    if (!validModes.includes(this.config.approvalMode)) {
      throw new Error(`Invalid approval mode: ${this.config.approvalMode}`);
    }

    logInfo(`Configuration validated successfully - Model: ${this.config.model}, Provider: ${this.config.provider}, Approval: ${this.config.approvalMode}`);
  }

  /**
   * Check API connectivity (non-blocking)
   */
  private async checkAPIConnectivity(): Promise<void> {
    try {
      const { testClientConnection } = await import('./utils/openai-client.js');
      const result = await testClientConnection(this.config.provider);
      
      if (!result.success) {
        console.log(chalk.yellow('⚠ Warning: Could not connect to AI provider'));
        console.log(chalk.gray(`  ${result.error}`));
        console.log(chalk.gray('  You may experience issues during conversation'));
        console.log();
      } else {
        logInfo('API connectivity check passed');
      }
    } catch (_error) {
      // Non-blocking - just log the warning
      console.log(chalk.yellow('⚠ Warning: Could not test API connectivity'));
      console.log(chalk.gray('  Proceeding anyway...'));
      console.log();
    }
  }

  /**
   * Initialize terminal UI
   */
  private async initializeUI(): Promise<void> {
    try {
      // Show startup message
      this.showStartupMessage();

      // Create and render the main terminal chat component
      this.inkInstance = render(
        React.createElement(TerminalChat, {
          config: this.config,
          onExit: () => this.handleExit()
        })
      );

      // Handle process signals
      this.setupSignalHandlers();

    } catch (error) {
      throw new Error(`Failed to initialize UI: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Show startup message
   */
  private showStartupMessage(): void {
    console.clear();
    
    console.log(chalk.blue.bold('🤖 Kritrima AI CLI'));
    console.log(chalk.gray(`   Sophisticated AI-powered assistant`));
    console.log();
    
    console.log(chalk.blue('Configuration:'));
    console.log(chalk.gray(`  Provider: ${this.config.provider}`));
    console.log(chalk.gray(`  Model: ${this.config.model}`));
    console.log(chalk.gray(`  Approval: ${this.config.approvalMode}`));

    // Show system prompt configuration if available
    if (this.config.systemPromptMode || this.config.safetyLevel || this.config.verbosityLevel) {
      console.log(chalk.blue('System Prompt:'));
      if (this.config.systemPromptMode) {
        console.log(chalk.gray(`  Mode: ${this.config.systemPromptMode}`));
      }
      if (this.config.systemPromptContext) {
        console.log(chalk.gray(`  Context: ${this.config.systemPromptContext}`));
      }
      if (this.config.safetyLevel) {
        console.log(chalk.gray(`  Safety: ${this.config.safetyLevel}`));
      }
      if (this.config.verbosityLevel) {
        console.log(chalk.gray(`  Verbosity: ${this.config.verbosityLevel}`));
      }
    }
    console.log();
    
    console.log(chalk.blue('Quick Help:'));
    console.log(chalk.gray('  /help     - Show available commands'));
    console.log(chalk.gray('  /model    - Switch AI model'));
    console.log(chalk.gray('  /history  - View command history'));
    console.log(chalk.gray('  Ctrl+C    - Exit application'));
    console.log();
    
    console.log(chalk.green('Ready! Type your message below:'));
    console.log(chalk.gray('─'.repeat(50)));
    console.log();
  }

  /**
   * Setup signal handlers for graceful shutdown
   */
  private setupSignalHandlers(): void {
    const handleShutdown = (signal: string) => {
      logInfo(`Received ${signal}, shutting down gracefully`);
      this.stop().then(() => {
        process.exit(0);
      }).catch(() => {
        process.exit(1);
      });
    };

    process.on('SIGINT', () => handleShutdown('SIGINT'));
    process.on('SIGTERM', () => handleShutdown('SIGTERM'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logError('Uncaught exception', error);
      console.error(chalk.red('Fatal error:'), error.message);
      this.stop().then(() => {
        process.exit(1);
      });
    });

    process.on('unhandledRejection', (reason) => {
      logError('Unhandled rejection', reason instanceof Error ? reason : new Error(String(reason)));
      console.error(chalk.red('Unhandled rejection:'), reason);
      this.stop().then(() => {
        process.exit(1);
      });
    });
  }

  /**
   * Handle application exit
   */
  private handleExit(): void {
    console.log(chalk.blue('\nThank you for using Kritrima AI CLI!'));
    console.log(chalk.gray('Goodbye! 👋'));
    
    this.stop().then(() => {
      process.exit(0);
    }).catch(() => {
      process.exit(1);
    });
  }

  /**
   * Get current configuration
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<AppConfig>): void {
    this.config = { ...this.config, ...updates };
    logInfo(`Configuration updated: ${JSON.stringify(updates)}`);
  }

  /**
   * Restart application with new configuration
   */
  async restart(newConfig?: Partial<AppConfig>): Promise<void> {
    if (newConfig) {
      this.updateConfig(newConfig);
    }

    await this.stop();
    await this.start();
  }
}

/**
 * Create and start application instance
 */
export async function createApp(config: AppConfig): Promise<App> {
  const app = new App(config);
  await app.start();
  return app;
}

/**
 * Application factory with error handling
 */
export async function startApplication(config: AppConfig): Promise<void> {
  try {
    const app = new App(config);
    await app.start();
  } catch (error) {
    console.error(chalk.red('Failed to start application:'));
    console.error(chalk.red(error instanceof Error ? error.message : 'Unknown error'));
    
    if (process.env.DEBUG) {
      console.error(chalk.gray('Stack trace:'));
      console.error(error instanceof Error ? error.stack : error);
    }
    
    process.exit(1);
  }
}
