import { randomBytes } from 'crypto';
export function generateSessionId() {
    const timestamp = Date.now().toString(36);
    const randomPart = randomBytes(6).toString('hex');
    return `session_${timestamp}_${randomPart}`;
}
export function isValidSessionId(sessionId) {
    const sessionIdPattern = /^session_[a-z0-9]+_[a-f0-9]{12}$/;
    return sessionIdPattern.test(sessionId);
}
export function getSessionTimestamp(sessionId) {
    try {
        if (!isValidSessionId(sessionId)) {
            return null;
        }
        const parts = sessionId.split('_');
        if (parts.length >= 2) {
            const timestampPart = parts[1];
            return parseInt(timestampPart, 36);
        }
        return null;
    }
    catch {
        return null;
    }
}
export function getShortSessionId(sessionId) {
    if (!isValidSessionId(sessionId)) {
        return sessionId.substring(0, 8);
    }
    const parts = sessionId.split('_');
    if (parts.length >= 3) {
        return `${parts[1].substring(0, 4)}...${parts[2].substring(0, 4)}`;
    }
    return sessionId.substring(0, 8);
}
export function createSessionMetadata(sessionId, additionalData) {
    return {
        id: sessionId,
        timestamp: Date.now(),
        shortId: getShortSessionId(sessionId),
        created: new Date().toISOString(),
        ...additionalData
    };
}
