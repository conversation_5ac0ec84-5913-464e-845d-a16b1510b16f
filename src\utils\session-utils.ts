/**
 * Session Utilities
 * 
 * Utilities for session management, ID generation, and session operations
 */

import { randomBytes } from 'crypto';

/**
 * Generate a unique session ID
 */
export function generateSessionId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = randomBytes(6).toString('hex');
  return `session_${timestamp}_${randomPart}`;
}

/**
 * Validate session ID format
 */
export function isValidSessionId(sessionId: string): boolean {
  const sessionIdPattern = /^session_[a-z0-9]+_[a-f0-9]{12}$/;
  return sessionIdPattern.test(sessionId);
}

/**
 * Extract timestamp from session ID
 */
export function getSessionTimestamp(sessionId: string): number | null {
  try {
    if (!isValidSessionId(sessionId)) {
      return null;
    }
    
    const parts = sessionId.split('_');
    if (parts.length >= 2) {
      const timestampPart = parts[1];
      return parseInt(timestampPart, 36);
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Generate a short session ID for display purposes
 */
export function getShortSessionId(sessionId: string): string {
  if (!isValidSessionId(sessionId)) {
    return sessionId.substring(0, 8);
  }
  
  const parts = sessionId.split('_');
  if (parts.length >= 3) {
    return `${parts[1].substring(0, 4)}...${parts[2].substring(0, 4)}`;
  }
  
  return sessionId.substring(0, 8);
}

/**
 * Create session metadata
 */
export function createSessionMetadata(sessionId: string, additionalData?: Record<string, any>) {
  return {
    id: sessionId,
    timestamp: Date.now(),
    shortId: getShortSessionId(sessionId),
    created: new Date().toISOString(),
    ...additionalData
  };
}
