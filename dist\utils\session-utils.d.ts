export declare function generateSessionId(): string;
export declare function isValidSessionId(sessionId: string): boolean;
export declare function getSessionTimestamp(sessionId: string): number | null;
export declare function getShortSessionId(sessionId: string): string;
export declare function createSessionMetadata(sessionId: string, additionalData?: Record<string, any>): {
    id: string;
    timestamp: number;
    shortId: string;
    created: string;
};
//# sourceMappingURL=session-utils.d.ts.map