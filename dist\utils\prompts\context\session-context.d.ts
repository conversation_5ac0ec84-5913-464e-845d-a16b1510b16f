import type { HistoryEntry } from '../../../types/index.js';
export interface SessionContext {
    id: string;
    startTime: number;
    duration: number;
    messageCount: number;
    lastActivity: number;
    preferences: {
        model: string;
        provider: string;
        approvalMode: string;
        verbosity: string;
        theme: string;
    };
    history: {
        commands: HistoryEntry[];
        recentCommands: string[];
        frequentCommands: string[];
        commandCount: number;
    };
    interaction: {
        totalSessions: number;
        averageSessionLength: number;
        preferredCommands: string[];
        errorRate: number;
        successRate: number;
    };
    current: {
        workingDirectory: string;
        activeFiles: string[];
        recentFiles: string[];
        openProjects: string[];
    };
    user: {
        expertise: string[];
        preferences: Record<string, any>;
        customInstructions?: string;
        learningMode: boolean;
    };
}
export declare function getSessionContext(): Promise<SessionContext>;
export declare function incrementMessageCount(): void;
export declare function resetSession(): void;
export declare function updateLastActivity(): void;
export declare function getSessionStats(): {
    id: string;
    startTime: number;
    duration: number;
    messageCount: number;
};
//# sourceMappingURL=session-context.d.ts.map