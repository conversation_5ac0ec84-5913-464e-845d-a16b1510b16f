import { readFileSync, existsSync, readdirSync, statSync } from 'fs';
import { join, basename, extname } from 'path';
import { execSync } from 'child_process';
import { logInfo, logError } from '../../logger/log.js';
export async function getProjectContext(projectPath = process.cwd()) {
    try {
        logInfo('Gathering project context', { projectPath });
        const context = {
            name: basename(projectPath),
            type: 'unknown',
            language: 'unknown',
            structure: await getProjectStructure(projectPath),
            dependencies: { production: {}, development: {}, total: 0 },
            scripts: {},
            configuration: await getProjectConfiguration(projectPath),
            build: {}
        };
        await detectProjectType(projectPath, context);
        if (context.configuration.hasGit) {
            context.git = await getGitInfo(projectPath);
        }
        logInfo('Project context gathered successfully', {
            name: context.name,
            type: context.type,
            language: context.language
        });
        return context;
    }
    catch (error) {
        logError('Failed to gather project context', error instanceof Error ? error : new Error(String(error)));
        return getMinimalProjectContext(projectPath);
    }
}
async function getProjectStructure(projectPath) {
    const structure = {
        directories: [],
        files: [],
        totalFiles: 0,
        totalDirectories: 0
    };
    try {
        const items = readdirSync(projectPath);
        for (const item of items) {
            if (item.startsWith('.') && !['package.json', 'tsconfig.json'].includes(item)) {
                continue;
            }
            if (['node_modules', 'dist', 'build', '.git'].includes(item)) {
                continue;
            }
            const itemPath = join(projectPath, item);
            const stats = statSync(itemPath);
            if (stats.isDirectory()) {
                structure.directories.push(item);
                structure.totalDirectories++;
            }
            else {
                structure.files.push(item);
                structure.totalFiles++;
            }
        }
        structure.directories.sort();
        structure.files.sort();
    }
    catch (error) {
        logError('Failed to read project structure', error instanceof Error ? error : new Error(String(error)));
    }
    return structure;
}
async function getProjectConfiguration(projectPath) {
    const config = {
        hasTypeScript: false,
        hasESLint: false,
        hasPrettier: false,
        hasJest: false,
        hasVitest: false,
        hasWebpack: false,
        hasVite: false,
        hasDocker: false,
        hasGit: false
    };
    const configFiles = {
        hasTypeScript: ['tsconfig.json', 'tsconfig.build.json'],
        hasESLint: ['.eslintrc', '.eslintrc.js', '.eslintrc.json', 'eslint.config.js'],
        hasPrettier: ['.prettierrc', '.prettierrc.js', '.prettierrc.json', 'prettier.config.js'],
        hasJest: ['jest.config.js', 'jest.config.json', 'jest.config.ts'],
        hasVitest: ['vitest.config.js', 'vitest.config.ts', 'vite.config.js', 'vite.config.ts'],
        hasWebpack: ['webpack.config.js', 'webpack.config.ts'],
        hasVite: ['vite.config.js', 'vite.config.ts'],
        hasDocker: ['Dockerfile', 'docker-compose.yml', 'docker-compose.yaml'],
        hasGit: ['.git']
    };
    for (const [key, files] of Object.entries(configFiles)) {
        for (const file of files) {
            if (existsSync(join(projectPath, file))) {
                config[key] = true;
                break;
            }
        }
    }
    return config;
}
async function detectProjectType(projectPath, context) {
    const packageJsonPath = join(projectPath, 'package.json');
    if (existsSync(packageJsonPath)) {
        await parsePackageJson(packageJsonPath, context);
        return;
    }
    const pythonFiles = ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'];
    for (const file of pythonFiles) {
        if (existsSync(join(projectPath, file))) {
            context.type = 'python';
            context.language = 'python';
            await parsePythonProject(projectPath, context);
            return;
        }
    }
    if (existsSync(join(projectPath, 'Cargo.toml'))) {
        context.type = 'rust';
        context.language = 'rust';
        await parseCargoToml(projectPath, context);
        return;
    }
    if (existsSync(join(projectPath, 'go.mod'))) {
        context.type = 'go';
        context.language = 'go';
        await parseGoMod(projectPath, context);
        return;
    }
    const javaFiles = ['pom.xml', 'build.gradle', 'build.gradle.kts'];
    for (const file of javaFiles) {
        if (existsSync(join(projectPath, file))) {
            context.type = 'java';
            context.language = 'java';
            context.framework = file.includes('gradle') ? 'gradle' : 'maven';
            return;
        }
    }
    const files = context.structure.files;
    const extensions = files.map(f => extname(f).toLowerCase());
    if (extensions.includes('.ts') || extensions.includes('.tsx')) {
        context.language = 'typescript';
        context.type = 'typescript';
    }
    else if (extensions.includes('.js') || extensions.includes('.jsx')) {
        context.language = 'javascript';
        context.type = 'javascript';
    }
    else if (extensions.includes('.py')) {
        context.language = 'python';
        context.type = 'python';
    }
    else if (extensions.includes('.rs')) {
        context.language = 'rust';
        context.type = 'rust';
    }
    else if (extensions.includes('.go')) {
        context.language = 'go';
        context.type = 'go';
    }
}
async function parsePackageJson(packageJsonPath, context) {
    try {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        context.name = packageJson.name || context.name;
        context.version = packageJson.version;
        context.description = packageJson.description;
        context.type = 'nodejs';
        if (context.configuration.hasTypeScript) {
            context.language = 'typescript';
        }
        else {
            context.language = 'javascript';
        }
        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
        if (deps.react)
            context.framework = 'react';
        else if (deps.vue)
            context.framework = 'vue';
        else if (deps.angular)
            context.framework = 'angular';
        else if (deps.express)
            context.framework = 'express';
        else if (deps.next)
            context.framework = 'nextjs';
        else if (deps.nuxt)
            context.framework = 'nuxtjs';
        else if (deps.svelte)
            context.framework = 'svelte';
        context.dependencies.production = packageJson.dependencies || {};
        context.dependencies.development = packageJson.devDependencies || {};
        context.dependencies.total = Object.keys(context.dependencies.production).length +
            Object.keys(context.dependencies.development).length;
        context.scripts = packageJson.scripts || {};
        context.build.entryPoint = packageJson.main || 'index.js';
        if (packageJson.scripts?.build) {
            if (packageJson.scripts.build.includes('webpack')) {
                context.build.buildTool = 'webpack';
            }
            else if (packageJson.scripts.build.includes('vite')) {
                context.build.buildTool = 'vite';
            }
            else if (packageJson.scripts.build.includes('tsc')) {
                context.build.buildTool = 'typescript';
            }
        }
    }
    catch (error) {
        logError('Failed to parse package.json', error instanceof Error ? error : new Error(String(error)));
    }
}
async function parsePythonProject(projectPath, context) {
    const reqPath = join(projectPath, 'requirements.txt');
    if (existsSync(reqPath)) {
        try {
            const requirements = readFileSync(reqPath, 'utf8')
                .split('\n')
                .filter(line => line.trim() && !line.startsWith('#'))
                .reduce((deps, line) => {
                const [name] = line.split(/[>=<]/);
                deps[name.trim()] = line.trim();
                return deps;
            }, {});
            context.dependencies.production = requirements;
            context.dependencies.total = Object.keys(requirements).length;
        }
        catch (error) {
            logError('Failed to parse requirements.txt', error instanceof Error ? error : new Error(String(error)));
        }
    }
}
async function parseCargoToml(projectPath, context) {
    const cargoPath = join(projectPath, 'Cargo.toml');
    try {
        const cargoContent = readFileSync(cargoPath, 'utf8');
        const nameMatch = cargoContent.match(/name\s*=\s*"([^"]+)"/);
        const versionMatch = cargoContent.match(/version\s*=\s*"([^"]+)"/);
        if (nameMatch)
            context.name = nameMatch[1];
        if (versionMatch)
            context.version = versionMatch[1];
    }
    catch (error) {
        logError('Failed to parse Cargo.toml', error instanceof Error ? error : new Error(String(error)));
    }
}
async function parseGoMod(projectPath, context) {
    const goModPath = join(projectPath, 'go.mod');
    try {
        const goModContent = readFileSync(goModPath, 'utf8');
        const moduleMatch = goModContent.match(/module\s+(.+)/);
        if (moduleMatch) {
            context.name = moduleMatch[1].split('/').pop() || context.name;
        }
    }
    catch (error) {
        logError('Failed to parse go.mod', error instanceof Error ? error : new Error(String(error)));
    }
}
async function getGitInfo(projectPath) {
    const git = {
        branch: 'unknown',
        remotes: [],
        status: 'unknown',
        lastCommit: undefined
    };
    try {
        git.branch = execSync('git branch --show-current', {
            cwd: projectPath,
            encoding: 'utf8'
        }).trim();
        const remotes = execSync('git remote -v', {
            cwd: projectPath,
            encoding: 'utf8'
        }).trim();
        git.remotes = remotes.split('\n').map(line => line.split('\t')[0]).filter(Boolean);
        git.status = execSync('git status --porcelain', {
            cwd: projectPath,
            encoding: 'utf8'
        }).trim() ? 'dirty' : 'clean';
        git.lastCommit = execSync('git log -1 --format="%h %s"', {
            cwd: projectPath,
            encoding: 'utf8'
        }).trim();
    }
    catch (error) {
        logError('Failed to get Git info', error instanceof Error ? error : new Error(String(error)));
    }
    return git;
}
function getMinimalProjectContext(projectPath) {
    return {
        name: basename(projectPath),
        type: 'unknown',
        language: 'unknown',
        structure: {
            directories: [],
            files: [],
            totalFiles: 0,
            totalDirectories: 0
        },
        dependencies: {
            production: {},
            development: {},
            total: 0
        },
        scripts: {},
        configuration: {
            hasTypeScript: false,
            hasESLint: false,
            hasPrettier: false,
            hasJest: false,
            hasVitest: false,
            hasWebpack: false,
            hasVite: false,
            hasDocker: false,
            hasGit: false
        },
        build: {}
    };
}
