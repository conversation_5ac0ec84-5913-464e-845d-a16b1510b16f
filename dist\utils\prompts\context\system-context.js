import { platform, release, arch, homedir, tmpdir, cpus, totalmem, freemem } from 'os';
import { existsSync, readFileSync } from 'fs';
import { execSync } from 'child_process';
import { logInfo, logError } from '../../logger/log.js';
export async function getSystemContext() {
    try {
        logInfo('Gathering system context');
        const context = {
            os: await getOSInfo(),
            node: getNodeInfo(),
            environment: getEnvironmentInfo(),
            hardware: getHardwareInfo(),
            capabilities: await getCapabilities(),
            runtime: getRuntimeInfo(),
            security: await getSecurityInfo()
        };
        logInfo('System context gathered successfully');
        return context;
    }
    catch (error) {
        logError('Failed to gather system context', error instanceof Error ? error : new Error(String(error)));
        return getMinimalContext();
    }
}
async function getOSInfo() {
    const osInfo = {
        platform: platform(),
        release: release(),
        arch: arch(),
        type: platform(),
        version: release()
    };
    try {
        if (platform() === 'linux') {
            if (existsSync('/etc/os-release')) {
                const osRelease = readFileSync('/etc/os-release', 'utf8');
                const nameMatch = osRelease.match(/PRETTY_NAME="([^"]+)"/);
                if (nameMatch) {
                    osInfo.version = nameMatch[1];
                }
            }
        }
        else if (platform() === 'darwin') {
            try {
                const version = execSync('sw_vers -productVersion', { encoding: 'utf8' }).trim();
                osInfo.version = `macOS ${version}`;
            }
            catch {
            }
        }
        else if (platform() === 'win32') {
            try {
                const version = execSync('ver', { encoding: 'utf8' }).trim();
                osInfo.version = version;
            }
            catch {
            }
        }
    }
    catch {
    }
    return osInfo;
}
function getNodeInfo() {
    return {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        execPath: process.execPath
    };
}
function getEnvironmentInfo() {
    const sensitiveKeys = ['PASSWORD', 'SECRET', 'KEY', 'TOKEN', 'API_KEY', 'AUTH'];
    const filteredEnv = {};
    for (const [key, value] of Object.entries(process.env)) {
        if (value && !sensitiveKeys.some(sensitive => key.toUpperCase().includes(sensitive))) {
            filteredEnv[key] = value;
        }
    }
    return {
        workingDirectory: process.cwd(),
        homeDirectory: homedir(),
        tempDirectory: tmpdir(),
        pathSeparator: platform() === 'win32' ? '\\' : '/',
        environmentVariables: filteredEnv
    };
}
function getHardwareInfo() {
    const cpuInfo = cpus();
    return {
        cpuCount: cpuInfo.length,
        totalMemory: totalmem(),
        freeMemory: freemem(),
        cpuModel: cpuInfo[0]?.model
    };
}
async function getCapabilities() {
    const capabilities = {
        hasGit: false,
        hasDocker: false,
        hasNode: true,
        hasNpm: false,
        hasPython: false,
        hasJava: false,
        availableShells: []
    };
    const tools = [
        { name: 'git', command: 'git --version', key: 'hasGit' },
        { name: 'docker', command: 'docker --version', key: 'hasDocker' },
        { name: 'npm', command: 'npm --version', key: 'hasNpm' },
        { name: 'python', command: 'python --version', key: 'hasPython' },
        { name: 'java', command: 'java -version', key: 'hasJava' }
    ];
    for (const tool of tools) {
        try {
            execSync(tool.command, { stdio: 'ignore', timeout: 5000 });
            capabilities[tool.key] = true;
        }
        catch {
        }
    }
    const shells = platform() === 'win32'
        ? ['cmd', 'powershell', 'pwsh']
        : ['bash', 'sh', 'zsh', 'fish'];
    for (const shell of shells) {
        try {
            execSync(platform() === 'win32' ? `where ${shell}` : `which ${shell}`, {
                stdio: 'ignore',
                timeout: 2000
            });
            capabilities.availableShells.push(shell);
        }
        catch {
        }
    }
    return capabilities;
}
function getRuntimeInfo() {
    return {
        timestamp: Date.now(),
        uptime: process.uptime() * 1000,
        processId: process.pid,
        parentProcessId: process.ppid
    };
}
async function getSecurityInfo() {
    const security = {
        isElevated: false,
        canWriteToSystem: false,
        sandboxed: false
    };
    try {
        if (platform() === 'win32') {
            try {
                execSync('net session', { stdio: 'ignore', timeout: 2000 });
                security.isElevated = true;
            }
            catch {
            }
        }
        else {
            security.isElevated = process.getuid ? process.getuid() === 0 : false;
        }
        const systemPaths = platform() === 'win32'
            ? ['C:\\Windows\\System32']
            : ['/usr/bin', '/etc'];
        for (const path of systemPaths) {
            try {
                if (existsSync(path)) {
                    security.canWriteToSystem = false;
                    break;
                }
            }
            catch {
            }
        }
        security.sandboxed = !!(process.env.SANDBOX ||
            process.env.FLATPAK_ID ||
            process.env.SNAP ||
            process.env.APPIMAGE);
    }
    catch (error) {
        logError('Failed to get security info', error instanceof Error ? error : new Error(String(error)));
    }
    return security;
}
function getMinimalContext() {
    return {
        os: {
            platform: platform(),
            release: release(),
            arch: arch(),
            type: platform(),
            version: release()
        },
        node: {
            version: process.version,
            platform: process.platform,
            arch: process.arch,
            execPath: process.execPath
        },
        environment: {
            workingDirectory: process.cwd(),
            homeDirectory: homedir(),
            tempDirectory: tmpdir(),
            pathSeparator: platform() === 'win32' ? '\\' : '/',
            environmentVariables: {}
        },
        hardware: {
            cpuCount: 1,
            totalMemory: 0,
            freeMemory: 0
        },
        capabilities: {
            hasGit: false,
            hasDocker: false,
            hasNode: true,
            hasNpm: false,
            hasPython: false,
            hasJava: false,
            availableShells: []
        },
        runtime: {
            timestamp: Date.now(),
            uptime: 0,
            processId: process.pid
        },
        security: {
            isElevated: false,
            canWriteToSystem: false,
            sandboxed: false
        }
    };
}
