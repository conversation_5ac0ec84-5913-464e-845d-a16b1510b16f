import type { ResponseInputItem, MessageContent } from '../types/index.js';
export declare function createInputItem(text: string, contentOrImagePaths?: MessageContent[] | string[]): Promise<ResponseInputItem>;
export declare function extractImagePaths(text: string): {
    cleanText: string;
    imagePaths: string[];
};
export declare function validateImagePaths(imagePaths: string[]): {
    valid: string[];
    invalid: string[];
};
export declare function getImageInfo(imagePath: string): {
    exists: boolean;
    size?: number;
    format?: string;
    supported?: boolean;
};
export declare function formatFileSize(bytes: number): string;
export declare function createSystemMessage(content: string): ResponseInputItem;
export declare function createAssistantMessage(content: string): ResponseInputItem;
export declare function validateInputItem(item: ResponseInputItem): {
    valid: boolean;
    errors: string[];
};
export declare function sanitizeTextInput(text: string): string;
//# sourceMappingURL=input-utils.d.ts.map