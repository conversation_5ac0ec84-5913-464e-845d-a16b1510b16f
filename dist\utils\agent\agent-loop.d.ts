import type { ApprovalPolicy, ResponseItem, ResponseInputItem, ResponseFunctionToolCall, ResponseToolResult } from '../../types/index.js';
export interface AgentLoopConfig {
    model: string;
    provider: string;
    approvalPolicy: ApprovalPolicy;
    maxIterations?: number;
    timeout?: number;
    additionalWritableRoots?: string[];
    singlePass?: boolean;
    planningMode?: boolean;
    executionMode?: boolean;
    validationMode?: boolean;
    useFullContext?: boolean;
    enableStreaming?: boolean;
    enableToolRegistry?: boolean;
    enableSessionPersistence?: boolean;
    enableContextOptimization?: boolean;
    enablePerformanceMonitoring?: boolean;
    sessionId?: string;
    maxContextTokens?: number;
    contextCompressionThreshold?: number;
}
export interface AgentLoopCallbacks {
    onDelta?: (delta: string) => void;
    onComplete?: (content: string) => void;
    onError?: (error: string) => void;
    onToolCall?: (toolCall: ResponseFunctionToolCall) => void;
    onToolResult?: (result: ResponseToolResult) => void;
    getCommandConfirmation?: (command: string[], workdir: string) => Promise<boolean>;
    onIterationStart?: (iteration: number) => void;
    onIterationComplete?: (iteration: number, result: any) => void;
    onContextOptimization?: (beforeTokens: number, afterTokens: number) => void;
    onSessionSave?: (sessionId: string) => void;
    onSessionRestore?: (sessionId: string) => void;
    onPerformanceMetrics?: (metrics: PerformanceMetrics) => void;
    onStateChange?: (state: AgentState) => void;
}
export interface PerformanceMetrics {
    totalExecutionTime: number;
    averageIterationTime: number;
    tokenUsage: number;
    toolCallCount: number;
    errorCount: number;
    successRate: number;
}
export type AgentState = 'idle' | 'thinking' | 'tool_calling' | 'waiting_approval' | 'error' | 'complete';
export declare class AgentLoop {
    private model;
    private provider;
    private oai;
    private approvalPolicy;
    private transcript;
    private cumulativeThinkingMs;
    private additionalWritableRoots;
    private config;
    private sessionId;
    private currentState;
    private performanceMetrics;
    private enableStreaming;
    private enableToolRegistry;
    private enableSessionPersistence;
    private enableContextOptimization;
    private enablePerformanceMonitoring;
    private maxContextTokens;
    private contextCompressionThreshold;
    private iterationTimes;
    private toolCallCount;
    private errorCount;
    private successfulIterations;
    constructor(config: AgentLoopConfig);
    private generateSessionId;
    private updateState;
    private updatePerformanceMetrics;
    private optimizeContext;
    private saveSession;
    executeLoop(userInput: ResponseInputItem, callbacks?: AgentLoopCallbacks, maxIterations?: number): Promise<ResponseItem[]>;
    executeLoop(userInput: ResponseInputItem, options: {
        callbacks?: AgentLoopCallbacks;
        maxIterations?: number;
        singlePass?: boolean;
        planningMode?: boolean;
        executionMode?: boolean;
        validationMode?: boolean;
    }): Promise<ResponseItem[]>;
    private handleFunctionCall;
    private handleShellCommand;
    private isCommandSafe;
    private getAvailableTools;
    getTranscript(): ResponseInputItem[];
    clearTranscript(): void;
    getCumulativeThinkingTime(): number;
    updateConfig(newConfig: Partial<AgentLoopConfig>): void;
    getCurrentState(): AgentState;
    getPerformanceMetrics(): PerformanceMetrics;
    getSessionId(): string;
    loadSession(sessionId: string): Promise<boolean>;
    reset(): void;
}
//# sourceMappingURL=agent-loop.d.ts.map