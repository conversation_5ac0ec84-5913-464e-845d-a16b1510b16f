/**
 * Integration tests for message conversion fix
 * Tests the fix for the "Empty input messages" issue
 */

import { describe, it, expect } from 'vitest';
import { convertMessagesToOpenAI, debugMessageConversion } from '../utils/responses.js';
import { createInputItem, validateInputItem } from '../utils/input-utils.js';
import type { ResponseItem, ResponseInputItem } from '../types/index.js';

describe('Message Conversion Fix', () => {
  describe('convertMessagesToOpenAI', () => {
    it('should handle input type items correctly', () => {
      const inputItem: ResponseInputItem = {
        type: 'input',
        role: 'user',
        content: [
          {
            type: 'input_text',
            text: 'Hello, test message'
          }
        ],
        timestamp: Date.now()
      };

      const messages = convertMessagesToOpenAI([inputItem]);
      
      expect(messages).toHaveLength(1);
      expect(messages[0]).toEqual({
        role: 'user',
        content: 'Hello, test message'
      });
    });

    it('should handle message type items correctly', () => {
      const messageItem: ResponseInputItem = {
        type: 'message',
        role: 'user',
        content: [
          {
            type: 'input_text',
            text: 'Hello, test message'
          }
        ],
        timestamp: Date.now()
      };

      const messages = convertMessagesToOpenAI([messageItem]);
      
      expect(messages).toHaveLength(1);
      expect(messages[0]).toEqual({
        role: 'user',
        content: 'Hello, test message'
      });
    });

    it('should handle mixed content types', () => {
      const inputItem: ResponseInputItem = {
        type: 'input',
        role: 'user',
        content: [
          {
            type: 'input_text',
            text: 'Hello'
          },
          {
            type: 'input_image',
            image: {
              url: 'data:image/png;base64,test',
              detail: 'high'
            }
          }
        ],
        timestamp: Date.now()
      };

      const messages = convertMessagesToOpenAI([inputItem]);
      
      expect(messages).toHaveLength(1);
      expect(messages[0].role).toBe('user');
      expect(Array.isArray(messages[0].content)).toBe(true);
      expect(messages[0].content).toHaveLength(2);
      expect(messages[0].content[0]).toEqual({
        type: 'text',
        text: 'Hello'
      });
      expect(messages[0].content[1]).toEqual({
        type: 'image_url',
        image_url: {
          url: 'data:image/png;base64,test',
          detail: 'high'
        }
      });
    });

    it('should skip items with no valid content', () => {
      const emptyItem: ResponseInputItem = {
        type: 'input',
        role: 'user',
        content: [],
        timestamp: Date.now()
      };

      const messages = convertMessagesToOpenAI([emptyItem]);
      expect(messages).toHaveLength(0);
    });

    it('should handle output items correctly', () => {
      const outputItem = {
        type: 'output' as const,
        role: 'assistant' as const,
        content: 'AI response here',
        timestamp: Date.now()
      };

      const messages = convertMessagesToOpenAI([outputItem]);
      
      expect(messages).toHaveLength(1);
      expect(messages[0]).toEqual({
        role: 'assistant',
        content: 'AI response here'
      });
    });
  });

  describe('createInputItem', () => {
    it('should create valid input items', async () => {
      const inputItem = await createInputItem('Hello, world!');
      
      expect(inputItem.type).toBe('input');
      expect(inputItem.role).toBe('user');
      expect(inputItem.content).toHaveLength(1);
      expect(inputItem.content[0]).toEqual({
        type: 'input_text',
        text: 'Hello, world!'
      });
    });

    it('should sanitize input text', async () => {
      const inputItem = await createInputItem('  Hello\x00world\x01  ');
      
      expect(inputItem.content[0].text).toBe('Helloworld');
    });

    it('should throw error for empty input', async () => {
      await expect(createInputItem('')).rejects.toThrow('Invalid input item');
    });

    it('should throw error for whitespace-only input', async () => {
      await expect(createInputItem('   ')).rejects.toThrow('Invalid input item');
    });
  });

  describe('validateInputItem', () => {
    it('should validate correct input items', () => {
      const validItem: ResponseInputItem = {
        type: 'input',
        role: 'user',
        content: [
          {
            type: 'input_text',
            text: 'Valid message'
          }
        ],
        timestamp: Date.now()
      };

      const result = validateInputItem(validItem);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject items with no content', () => {
      const invalidItem: ResponseInputItem = {
        type: 'input',
        role: 'user',
        content: [],
        timestamp: Date.now()
      };

      const result = validateInputItem(invalidItem);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Input item content cannot be empty');
    });

    it('should reject items with invalid role', () => {
      const invalidItem: any = {
        type: 'input',
        role: 'invalid',
        content: [{ type: 'input_text', text: 'test' }],
        timestamp: Date.now()
      };

      const result = validateInputItem(invalidItem);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Input item must have valid role (user, assistant, or system)');
    });
  });

  describe('debugMessageConversion', () => {
    it('should provide debug information', () => {
      const items: ResponseItem[] = [
        {
          type: 'input',
          role: 'user',
          content: [{ type: 'input_text', text: 'Hello' }],
          timestamp: Date.now()
        },
        {
          type: 'output',
          role: 'assistant',
          content: 'Hi there!',
          timestamp: Date.now()
        }
      ];

      const debug = debugMessageConversion(items);
      
      expect(debug.inputItems).toBe(1);
      expect(debug.outputItems).toBe(1);
      expect(debug.convertedMessages).toBe(2);
      expect(debug.details).toHaveLength(2);
    });
  });
});
