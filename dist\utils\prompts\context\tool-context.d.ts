import type { AppConfig } from '../../../types/index.js';
export interface ToolContext {
    available: string[];
    permissions: {
        canExecuteCommands: boolean;
        canWriteFiles: boolean;
        canReadFiles: boolean;
        canInstallPackages: boolean;
        canModifySystem: boolean;
        approvalRequired: string[];
    };
    restrictions: {
        safeCommands: string[];
        dangerousCommands: string[];
        blockedPaths: string[];
        timeoutLimits: Record<string, number>;
    };
    capabilities: {
        shellAccess: boolean;
        fileSystem: boolean;
        networkAccess: boolean;
        packageManagement: boolean;
        gitOperations: boolean;
        dockerAccess: boolean;
        systemInfo: boolean;
    };
    functions: {
        builtin: BuiltinFunction[];
        custom: CustomFunction[];
        total: number;
    };
    environment: {
        shell: string;
        workingDirectory: string;
        pathSeparator: string;
        environmentVariables: string[];
    };
    limits: {
        maxExecutionTime: number;
        maxFileSize: number;
        maxOutputLength: number;
        concurrentOperations: number;
    };
}
export interface BuiltinFunction {
    name: string;
    description: string;
    category: string;
    parameters: string[];
    examples: string[];
    restrictions?: string[];
}
export interface CustomFunction {
    name: string;
    description: string;
    source: string;
    enabled: boolean;
}
export declare function getToolContext(config: AppConfig): Promise<ToolContext>;
export declare function isCommandSafe(command: string[], config: AppConfig): boolean;
export declare function getToolUsageStats(): {
    mostUsedTools: never[];
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
};
//# sourceMappingURL=tool-context.d.ts.map