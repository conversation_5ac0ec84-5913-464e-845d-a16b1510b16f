import { createOpenAIClient } from '../openai-client.js';
import { loadConfig } from '../config.js';
import { logAgentExecution, logError, logInfo } from '../logger/log.js';
import { handleExecCommand } from './handle-exec-command.js';
import { convertMessagesToOpenAI, estimateTokenCount, debugMessageConversion } from '../responses.js';
import { toolRegistry } from '../tools/tool-registry.js';
import { saveRollout, loadSession } from '../storage/save-rollout.js';
import { generateSystemPrompt } from '../prompts/system-prompt-manager.js';
import { createSystemMessage } from '../input-utils.js';
export class AgentLoop {
    model;
    provider;
    oai;
    approvalPolicy;
    transcript = [];
    cumulativeThinkingMs = 0;
    additionalWritableRoots;
    config;
    sessionId;
    currentState = 'idle';
    performanceMetrics;
    enableStreaming;
    enableToolRegistry;
    enableSessionPersistence;
    enableContextOptimization;
    enablePerformanceMonitoring;
    maxContextTokens;
    contextCompressionThreshold;
    iterationTimes = [];
    toolCallCount = 0;
    errorCount = 0;
    successfulIterations = 0;
    enableSystemPrompt;
    systemPromptMode;
    systemPromptContext;
    customInstructions;
    safetyLevel;
    verbosityLevel;
    constructor(config) {
        this.model = config.model;
        this.provider = config.provider;
        this.approvalPolicy = config.approvalPolicy;
        this.additionalWritableRoots = config.additionalWritableRoots || [];
        this.config = loadConfig();
        this.sessionId = config.sessionId || this.generateSessionId();
        this.enableStreaming = config.enableStreaming ?? true;
        this.enableToolRegistry = config.enableToolRegistry ?? true;
        this.enableSessionPersistence = config.enableSessionPersistence ?? true;
        this.enableContextOptimization = config.enableContextOptimization ?? true;
        this.enablePerformanceMonitoring = config.enablePerformanceMonitoring ?? true;
        this.maxContextTokens = config.maxContextTokens || 32000;
        this.contextCompressionThreshold = config.contextCompressionThreshold || 0.8;
        this.enableSystemPrompt = config.enableSystemPrompt ?? true;
        this.systemPromptMode = config.systemPromptMode || 'general';
        this.systemPromptContext = config.systemPromptContext || 'agent-loop';
        this.customInstructions = config.customInstructions;
        this.safetyLevel = config.safetyLevel || 'moderate';
        this.verbosityLevel = config.verbosityLevel || 'normal';
        this.performanceMetrics = {
            totalExecutionTime: 0,
            averageIterationTime: 0,
            tokenUsage: 0,
            toolCallCount: 0,
            errorCount: 0,
            successRate: 0
        };
        this.oai = createOpenAIClient({
            provider: this.provider,
            timeout: config.timeout
        });
        logInfo(`AgentLoop initialized with session ID: ${this.sessionId}`);
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    async addSystemPrompt() {
        try {
            logInfo('Generating system prompt', {
                mode: this.systemPromptMode,
                context: this.systemPromptContext,
                provider: this.provider
            });
            const systemPromptResult = await generateSystemPrompt(this.systemPromptMode, this.systemPromptContext, this.config, {
                customInstructions: this.customInstructions,
                safetyLevel: this.safetyLevel,
                verbosityLevel: this.verbosityLevel,
                enableThinking: true,
                enablePlanning: this.systemPromptMode === 'planning',
                enableValidation: this.systemPromptMode === 'validation'
            });
            const systemMessage = createSystemMessage(systemPromptResult.prompt);
            this.transcript.push(systemMessage);
            logInfo('System prompt added to transcript', {
                templateId: systemPromptResult.metadata.templateId,
                promptLength: systemPromptResult.prompt.length,
                tokenCount: systemPromptResult.metadata.tokenCount
            });
        }
        catch (error) {
            logError('Failed to generate system prompt', error instanceof Error ? error : new Error(String(error)));
            const fallbackPrompt = this.createFallbackSystemPrompt();
            const systemMessage = createSystemMessage(fallbackPrompt);
            this.transcript.push(systemMessage);
        }
    }
    createFallbackSystemPrompt() {
        return `You are Kritrima AI, an advanced AI assistant with comprehensive capabilities.

# Core Instructions
- Be helpful, accurate, and efficient
- Use available tools when appropriate
- Follow safety guidelines and best practices
- Provide clear explanations and step-by-step guidance

# Current Configuration
- Model: ${this.model}
- Provider: ${this.provider}
- Mode: ${this.systemPromptMode}
- Context: ${this.systemPromptContext}
- Safety Level: ${this.safetyLevel}
- Verbosity: ${this.verbosityLevel}

${this.customInstructions ? `# Custom Instructions\n${this.customInstructions}\n` : ''}

Ready to assist you with your tasks!`;
    }
    updateState(newState, callbacks) {
        if (this.currentState !== newState) {
            this.currentState = newState;
            callbacks?.onStateChange?.(newState);
            logAgentExecution('state_change', { state: newState });
        }
    }
    updatePerformanceMetrics(callbacks) {
        if (!this.enablePerformanceMonitoring)
            return;
        const totalIterations = this.successfulIterations + this.errorCount;
        this.performanceMetrics = {
            totalExecutionTime: this.cumulativeThinkingMs,
            averageIterationTime: this.iterationTimes.length > 0
                ? this.iterationTimes.reduce((a, b) => a + b, 0) / this.iterationTimes.length
                : 0,
            tokenUsage: this.performanceMetrics.tokenUsage,
            toolCallCount: this.toolCallCount,
            errorCount: this.errorCount,
            successRate: totalIterations > 0 ? this.successfulIterations / totalIterations : 0
        };
        callbacks?.onPerformanceMetrics?.(this.performanceMetrics);
    }
    async optimizeContext(callbacks) {
        if (!this.enableContextOptimization)
            return;
        const messages = convertMessagesToOpenAI([...this.transcript]);
        const currentTokens = estimateTokenCount(messages);
        if (currentTokens > this.maxContextTokens * this.contextCompressionThreshold) {
            logInfo(`Context optimization triggered: ${currentTokens} tokens > ${this.maxContextTokens * this.contextCompressionThreshold}`);
            const keepCount = Math.min(5, this.transcript.length);
            const compressedTranscript = [
                ...this.transcript.slice(0, 1),
                {
                    type: 'message',
                    role: 'system',
                    content: [{
                            type: 'input_text',
                            text: `[Previous conversation compressed - ${this.transcript.length - keepCount - 1} messages summarized]`
                        }],
                    timestamp: Date.now()
                },
                ...this.transcript.slice(-keepCount)
            ];
            const newMessages = convertMessagesToOpenAI(compressedTranscript);
            const newTokens = estimateTokenCount(newMessages);
            this.transcript = compressedTranscript;
            logInfo(`Context optimized: ${currentTokens} -> ${newTokens} tokens`);
            callbacks?.onContextOptimization?.(currentTokens, newTokens);
        }
    }
    async saveSession(callbacks) {
        if (!this.enableSessionPersistence)
            return;
        try {
            saveRollout(this.sessionId, [...this.transcript], {
                model: this.model,
                provider: this.provider,
                approvalPolicy: this.approvalPolicy
            });
            callbacks?.onSessionSave?.(this.sessionId);
            logInfo(`Session saved: ${this.sessionId}`);
        }
        catch (error) {
            logError('Failed to save session', error instanceof Error ? error : new Error(String(error)));
        }
    }
    async executeLoop(userInput, callbacksOrOptions = {}, maxIterations = 10) {
        let callbacks = {};
        let actualMaxIterations = maxIterations;
        let singlePass = false;
        if ('callbacks' in callbacksOrOptions || 'maxIterations' in callbacksOrOptions) {
            const options = callbacksOrOptions;
            callbacks = options.callbacks || {};
            actualMaxIterations = options.maxIterations || 10;
            singlePass = options.singlePass || false;
        }
        else {
            callbacks = callbacksOrOptions;
        }
        if (singlePass) {
            actualMaxIterations = 1;
        }
        const startTime = Date.now();
        const results = [];
        try {
            this.updateState('thinking', callbacks);
            if (this.enableSystemPrompt && this.transcript.length === 0) {
                await this.addSystemPrompt();
            }
            this.transcript.push(userInput);
            results.push(userInput);
            await this.optimizeContext(callbacks);
            logAgentExecution('loop_start', {
                model: this.model,
                provider: this.provider,
                approvalPolicy: this.approvalPolicy,
                sessionId: this.sessionId
            });
            let iteration = 0;
            while (iteration < actualMaxIterations) {
                iteration++;
                callbacks.onIterationStart?.(iteration);
                logAgentExecution('iteration_start', { iteration });
                const messages = convertMessagesToOpenAI([...this.transcript]);
                if (process.env.DEBUG) {
                    const debugInfo = debugMessageConversion([...this.transcript]);
                    logAgentExecution('message_conversion_debug', debugInfo);
                }
                if (messages.length === 0) {
                    const debugInfo = debugMessageConversion([...this.transcript]);
                    const errorDetails = `Transcript items: ${debugInfo.inputItems} inputs, ${debugInfo.outputItems} outputs. Details: ${JSON.stringify(debugInfo.details)}`;
                    throw new Error(`No valid messages to send to AI provider. Check that your input contains text content. ${errorDetails}`);
                }
                const tokenCount = estimateTokenCount(messages);
                this.performanceMetrics.tokenUsage += tokenCount;
                logAgentExecution('token_count', { tokens: tokenCount, messageCount: messages.length });
                const tools = this.getAvailableTools();
                const iterationStartTime = Date.now();
                try {
                    const completion = await this.oai.chat.completions.create({
                        model: this.model,
                        messages,
                        tools: tools.length > 0 ? tools : undefined,
                        tool_choice: tools.length > 0 ? 'auto' : undefined,
                        temperature: this.config.temperature || 0.7,
                        max_tokens: this.config.maxTokens || 4096,
                        stream: false
                    });
                    const iterationTime = Date.now() - iterationStartTime;
                    this.cumulativeThinkingMs += iterationTime;
                    this.iterationTimes.push(iterationTime);
                    this.successfulIterations++;
                    const choice = completion.choices?.[0];
                    if (!choice) {
                        throw new Error('No response choice received');
                    }
                    const content = choice.message?.content || '';
                    if (content.trim()) {
                        callbacks.onDelta?.(content);
                        callbacks.onComplete?.(content);
                        const assistantResponse = {
                            role: 'assistant',
                            content,
                            type: 'output',
                            timestamp: Date.now(),
                            metadata: {
                                model: this.model,
                                provider: this.provider,
                                thinkingTime: iterationTime
                            }
                        };
                        results.push(assistantResponse);
                    }
                    if (choice.message?.tool_calls) {
                        this.updateState('tool_calling', callbacks);
                        for (const toolCall of choice.message.tool_calls) {
                            if (toolCall.type === 'function') {
                                this.toolCallCount++;
                                const functionCall = {
                                    id: toolCall.id,
                                    name: toolCall.function.name,
                                    arguments: toolCall.function.arguments,
                                    type: 'function_call',
                                    timestamp: Date.now()
                                };
                                results.push(functionCall);
                                callbacks.onToolCall?.(functionCall);
                                const toolResults = await this.handleFunctionCall(functionCall, callbacks);
                                results.push(...toolResults);
                            }
                        }
                        this.updatePerformanceMetrics(callbacks);
                    }
                    else {
                        this.updateState('complete', callbacks);
                        await this.saveSession(callbacks);
                        this.updatePerformanceMetrics(callbacks);
                        logAgentExecution('loop_complete', {
                            iterations: iteration,
                            totalTime: Date.now() - startTime,
                            sessionId: this.sessionId
                        });
                        callbacks.onIterationComplete?.(iteration, results);
                        return results;
                    }
                }
                catch (error) {
                    this.errorCount++;
                    this.updateState('error', callbacks);
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    logError('Agent loop iteration failed', error instanceof Error ? error : new Error(errorMessage));
                    callbacks.onError?.(errorMessage);
                    this.updatePerformanceMetrics(callbacks);
                    throw error;
                }
            }
            this.updateState('complete', callbacks);
            await this.saveSession(callbacks);
            this.updatePerformanceMetrics(callbacks);
            logAgentExecution('loop_max_iterations', { maxIterations: actualMaxIterations });
            return results;
        }
        catch (error) {
            this.updateState('error', callbacks);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            logError('Agent loop failed', error instanceof Error ? error : new Error(errorMessage));
            callbacks.onError?.(errorMessage);
            await this.saveSession(callbacks);
            this.updatePerformanceMetrics(callbacks);
            throw error;
        }
    }
    async handleFunctionCall(functionCall, callbacks) {
        const { name, arguments: argsString } = functionCall;
        try {
            let args;
            try {
                args = JSON.parse(argsString);
            }
            catch (error) {
                throw new Error(`Invalid function arguments: ${argsString}`);
            }
            if (this.enableToolRegistry) {
                const tool = toolRegistry.getTool(name);
                if (tool) {
                    const context = {
                        workingDirectory: process.cwd(),
                        environment: process.env,
                        capabilities: ['file_read', 'file_write', 'command_execute']
                    };
                    const toolResult = await toolRegistry.executeTool(name, args, context);
                    const result = {
                        id: functionCall.id,
                        result: toolResult.output || toolResult.data || 'Tool executed successfully',
                        success: toolResult.success,
                        type: 'tool_result',
                        timestamp: Date.now(),
                        metadata: {
                            ...toolResult.metadata,
                            toolId: name,
                            executionTime: toolResult.executionTime
                        }
                    };
                    return [result];
                }
            }
            if (name === 'shell' || name === 'local_shell') {
                return await this.handleShellCommand(args, functionCall.id, callbacks);
            }
            throw new Error(`Unknown function: ${name}`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorResult = {
                id: functionCall.id,
                result: `Error: ${errorMessage}`,
                success: false,
                type: 'tool_result',
                timestamp: Date.now()
            };
            return [errorResult];
        }
    }
    async handleShellCommand(args, toolCallId, callbacks) {
        try {
            const execInput = {
                command: args.command || [],
                workdir: args.workdir || process.cwd(),
                timeout: args.timeout || 30000
            };
            const needsApproval = this.approvalPolicy === 'suggest' ||
                (this.approvalPolicy === 'auto-edit' && !this.isCommandSafe(execInput.command));
            if (needsApproval && callbacks.getCommandConfirmation) {
                const approved = await callbacks.getCommandConfirmation(execInput.command, execInput.workdir || process.cwd());
                if (!approved) {
                    const deniedResult = {
                        id: toolCallId,
                        result: 'Command execution denied by user',
                        success: false,
                        type: 'tool_result',
                        timestamp: Date.now(),
                        metadata: {
                            command: execInput.command,
                            workdir: execInput.workdir
                        }
                    };
                    return [deniedResult];
                }
            }
            const execResult = await handleExecCommand(execInput, this.config, this.approvalPolicy, this.additionalWritableRoots);
            const toolResult = {
                id: toolCallId,
                result: execResult.output,
                success: execResult.success,
                type: 'tool_result',
                timestamp: Date.now(),
                metadata: {
                    command: execResult.command,
                    workdir: execResult.workdir,
                    exitCode: execResult.exitCode,
                    duration: execResult.duration
                }
            };
            callbacks.onToolResult?.(toolResult);
            return [toolResult];
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorResult = {
                id: toolCallId,
                result: `Error executing command: ${errorMessage}`,
                success: false,
                type: 'tool_result',
                timestamp: Date.now()
            };
            return [errorResult];
        }
    }
    isCommandSafe(command) {
        const safeCommands = this.config.safeCommands || [];
        const commandName = command[0]?.toLowerCase();
        return safeCommands.includes(commandName);
    }
    getAvailableTools() {
        const tools = [];
        if (this.enableToolRegistry) {
            const registryTools = toolRegistry.getAllTools();
            for (const tool of registryTools) {
                const functionTool = {
                    type: 'function',
                    function: {
                        name: tool.id,
                        description: tool.description,
                        parameters: {
                            type: 'object',
                            properties: {},
                            required: []
                        }
                    }
                };
                for (const param of tool.parameters) {
                    functionTool.function.parameters.properties[param.name] = {
                        type: param.type,
                        description: param.description
                    };
                    if (param.required) {
                        functionTool.function.parameters.required.push(param.name);
                    }
                }
                tools.push(functionTool);
            }
        }
        tools.push({
            type: 'function',
            function: {
                name: 'shell',
                description: 'Execute shell commands and return their output. Use this to run system commands, file operations, and interact with the environment.',
                parameters: {
                    type: 'object',
                    properties: {
                        command: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'The command to execute as an array of strings (command and arguments)'
                        },
                        workdir: {
                            type: 'string',
                            description: 'Working directory for command execution (optional, defaults to current directory)'
                        },
                        timeout: {
                            type: 'number',
                            description: 'Timeout in milliseconds (optional, defaults to 30000)'
                        }
                    },
                    required: ['command']
                }
            }
        });
        logInfo(`Available tools: ${tools.map(t => t.function.name).join(', ')}`);
        return tools;
    }
    getTranscript() {
        return [...this.transcript];
    }
    clearTranscript() {
        this.transcript = [];
    }
    getCumulativeThinkingTime() {
        return this.cumulativeThinkingMs;
    }
    updateConfig(newConfig) {
        if (newConfig.model)
            this.model = newConfig.model;
        if (newConfig.provider)
            this.provider = newConfig.provider;
        if (newConfig.approvalPolicy)
            this.approvalPolicy = newConfig.approvalPolicy;
        if (newConfig.additionalWritableRoots)
            this.additionalWritableRoots = newConfig.additionalWritableRoots;
        if (newConfig.enableStreaming !== undefined)
            this.enableStreaming = newConfig.enableStreaming;
        if (newConfig.enableToolRegistry !== undefined)
            this.enableToolRegistry = newConfig.enableToolRegistry;
        if (newConfig.enableSessionPersistence !== undefined)
            this.enableSessionPersistence = newConfig.enableSessionPersistence;
        if (newConfig.enableContextOptimization !== undefined)
            this.enableContextOptimization = newConfig.enableContextOptimization;
        if (newConfig.enablePerformanceMonitoring !== undefined)
            this.enablePerformanceMonitoring = newConfig.enablePerformanceMonitoring;
        if (newConfig.maxContextTokens)
            this.maxContextTokens = newConfig.maxContextTokens;
        if (newConfig.contextCompressionThreshold)
            this.contextCompressionThreshold = newConfig.contextCompressionThreshold;
        if (newConfig.enableSystemPrompt !== undefined)
            this.enableSystemPrompt = newConfig.enableSystemPrompt;
        if (newConfig.systemPromptMode)
            this.systemPromptMode = newConfig.systemPromptMode;
        if (newConfig.systemPromptContext)
            this.systemPromptContext = newConfig.systemPromptContext;
        if (newConfig.customInstructions !== undefined)
            this.customInstructions = newConfig.customInstructions;
        if (newConfig.safetyLevel)
            this.safetyLevel = newConfig.safetyLevel;
        if (newConfig.verbosityLevel)
            this.verbosityLevel = newConfig.verbosityLevel;
        if (newConfig.provider || newConfig.model) {
            this.oai = createOpenAIClient({
                provider: this.provider,
                timeout: newConfig.timeout
            });
        }
    }
    getCurrentState() {
        return this.currentState;
    }
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }
    getSessionId() {
        return this.sessionId;
    }
    async loadSession(sessionId) {
        try {
            const sessionData = loadSession(sessionId);
            if (sessionData) {
                this.transcript = sessionData.items.filter(item => item.type === 'input' || item.type === 'message');
                this.sessionId = sessionId;
                logInfo(`Session loaded: ${sessionId} with ${this.transcript.length} items`);
                return true;
            }
            return false;
        }
        catch (error) {
            logError('Failed to load session', error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }
    reset() {
        this.transcript = [];
        this.cumulativeThinkingMs = 0;
        this.iterationTimes = [];
        this.toolCallCount = 0;
        this.errorCount = 0;
        this.successfulIterations = 0;
        this.currentState = 'idle';
        this.sessionId = this.generateSessionId();
        this.performanceMetrics = {
            totalExecutionTime: 0,
            averageIterationTime: 0,
            tokenUsage: 0,
            toolCallCount: 0,
            errorCount: 0,
            successRate: 0
        };
        logInfo(`Agent reset with new session ID: ${this.sessionId}`);
    }
}
