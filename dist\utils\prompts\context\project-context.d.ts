export interface ProjectContext {
    name: string;
    type: string;
    language: string;
    framework?: string;
    version?: string;
    description?: string;
    structure: {
        directories: string[];
        files: string[];
        totalFiles: number;
        totalDirectories: number;
    };
    dependencies: {
        production: Record<string, string>;
        development: Record<string, string>;
        total: number;
    };
    scripts: Record<string, string>;
    configuration: {
        hasTypeScript: boolean;
        hasESLint: boolean;
        hasPrettier: boolean;
        hasJest: boolean;
        hasVitest: boolean;
        hasWebpack: boolean;
        hasVite: boolean;
        hasDocker: boolean;
        hasGit: boolean;
    };
    git?: {
        branch: string;
        remotes: string[];
        status: string;
        lastCommit?: string;
    };
    build: {
        outputDir?: string;
        entryPoint?: string;
        buildTool?: string;
    };
}
export declare function getProjectContext(projectPath?: string): Promise<ProjectContext>;
//# sourceMappingURL=project-context.d.ts.map