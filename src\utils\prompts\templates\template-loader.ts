/**
 * Template Loader
 * 
 * Loads and manages system prompt templates from various sources
 * including built-in templates, file system, and remote sources.
 */

import { readFileSync, existsSync, readdirSync } from 'fs';
import { join, extname } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import type { SystemPromptTemplate } from '../../../types/index.js';
import { logInfo, logError } from '../../logger/log.js';

/**
 * Get current directory for ES modules
 */
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Template directory paths
 */
const BUILTIN_TEMPLATES_DIR = join(__dirname, 'builtin');
const USER_TEMPLATES_DIR = join(process.cwd(), '.kritrima', 'templates');

/**
 * Template cache
 */
const templateCache = new Map<string, SystemPromptTemplate>();

/**
 * Load template by ID
 */
export function loadTemplate(templateId: string): SystemPromptTemplate {
  try {
    // Check cache first
    if (templateCache.has(templateId)) {
      return templateCache.get(templateId)!;
    }

    // Try to load from built-in templates
    const builtinPath = join(BUILTIN_TEMPLATES_DIR, `${templateId}.json`);
    if (existsSync(builtinPath)) {
      const template = loadTemplateFromFile(builtinPath);
      templateCache.set(templateId, template);
      return template;
    }

    // Try to load from user templates
    const userPath = join(USER_TEMPLATES_DIR, `${templateId}.json`);
    if (existsSync(userPath)) {
      const template = loadTemplateFromFile(userPath);
      templateCache.set(templateId, template);
      return template;
    }

    // Try built-in template definitions
    const builtinTemplate = getBuiltinTemplate(templateId);
    if (builtinTemplate) {
      templateCache.set(templateId, builtinTemplate);
      return builtinTemplate;
    }

    throw new Error(`Template not found: ${templateId}`);

  } catch (error) {
    logError(`Failed to load template: ${templateId}`, error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
}

/**
 * Get list of available template IDs
 */
export function getAvailableTemplates(): string[] {
  const templates = new Set<string>();

  try {
    // Add built-in template files
    if (existsSync(BUILTIN_TEMPLATES_DIR)) {
      const builtinFiles = readdirSync(BUILTIN_TEMPLATES_DIR)
        .filter(file => extname(file) === '.json')
        .map(file => file.replace('.json', ''));
      builtinFiles.forEach(id => templates.add(id));
    }

    // Add user template files
    if (existsSync(USER_TEMPLATES_DIR)) {
      const userFiles = readdirSync(USER_TEMPLATES_DIR)
        .filter(file => extname(file) === '.json')
        .map(file => file.replace('.json', ''));
      userFiles.forEach(id => templates.add(id));
    }

    // Add built-in template definitions
    const builtinIds = getBuiltinTemplateIds();
    builtinIds.forEach(id => templates.add(id));

  } catch (error) {
    logError('Failed to get available templates', error instanceof Error ? error : new Error(String(error)));
  }

  return Array.from(templates);
}

/**
 * Load template from file
 */
function loadTemplateFromFile(filePath: string): SystemPromptTemplate {
  try {
    const content = readFileSync(filePath, 'utf8');
    const template = JSON.parse(content) as SystemPromptTemplate;
    
    // Validate template structure
    validateTemplate(template);
    
    return template;

  } catch (error) {
    throw new Error(`Failed to load template from ${filePath}: ${error}`);
  }
}

/**
 * Validate template structure
 */
function validateTemplate(template: any): void {
  const required = ['id', 'name', 'description', 'mode', 'context', 'template', 'metadata'];
  
  for (const field of required) {
    if (!(field in template)) {
      throw new Error(`Template missing required field: ${field}`);
    }
  }

  if (typeof template.template !== 'string') {
    throw new Error('Template content must be a string');
  }

  if (!Array.isArray(template.context)) {
    throw new Error('Template context must be an array');
  }
}

/**
 * Get built-in template by ID
 */
function getBuiltinTemplate(templateId: string): SystemPromptTemplate | null {
  const templates = getBuiltinTemplates();
  return templates.find(t => t.id === templateId) || null;
}

/**
 * Get list of built-in template IDs
 */
function getBuiltinTemplateIds(): string[] {
  return getBuiltinTemplates().map(t => t.id);
}

/**
 * Get all built-in templates
 */
function getBuiltinTemplates(): SystemPromptTemplate[] {
  return [
    {
      id: 'general',
      name: 'General Assistant',
      description: 'General-purpose AI assistant for various tasks',
      mode: 'general',
      context: ['cli', 'interactive', 'agent-loop'],
      template: `You are Kritrima AI, an advanced AI assistant with comprehensive capabilities and access to various tools.

# Core Identity
You are a sophisticated AI assistant designed to help users with a wide range of tasks including:
- Software development and coding
- System administration and DevOps
- Data analysis and processing
- Documentation and writing
- Problem-solving and troubleshooting
- Learning and education

# Capabilities
{{#if tools.available}}
## Available Tools
{{#each tools.available}}
- {{this}}
{{/each}}
{{/if}}

{{#if system.os}}
## System Environment
- Operating System: {{system.os.platform}} {{system.os.version}}
- Architecture: {{system.os.arch}}
- Node.js: {{system.node.version}}
- Working Directory: {{system.environment.workingDirectory}}
{{/if}}

{{#if project}}
## Project Context
- Name: {{project.name}}
- Type: {{project.type}}
- Language: {{project.language}}
{{#if project.framework}}
- Framework: {{project.framework}}
{{/if}}
{{/if}}

# Guidelines
1. **Be Helpful**: Provide accurate, useful, and actionable assistance
2. **Be Safe**: Follow security best practices and ask for confirmation on potentially risky operations
3. **Be Efficient**: Use available tools effectively and provide concise solutions
4. **Be Clear**: Explain your reasoning and provide step-by-step instructions when needed
5. **Be Adaptive**: Adjust your approach based on user expertise and context

{{#if config.safetyLevel}}
# Safety Level: {{config.safetyLevel}}
{{#if (eq config.safetyLevel "strict")}}
- Always prioritize safety over convenience
- Require explicit confirmation for system modifications
- Refuse potentially harmful requests
{{else if (eq config.safetyLevel "moderate")}}
- Exercise caution with system operations
- Warn about potential risks
- Ask for confirmation on significant changes
{{else}}
- Proceed with user requests while noting risks
- Provide warnings for potentially dangerous operations
{{/if}}
{{/if}}

{{#if config.verbosityLevel}}
# Response Style: {{config.verbosityLevel}}
{{#if (eq config.verbosityLevel "minimal")}}
Provide concise, direct responses with minimal explanation.
{{else if (eq config.verbosityLevel "detailed")}}
Provide comprehensive responses with detailed explanations and examples.
{{else if (eq config.verbosityLevel "verbose")}}
Provide extensive responses with thorough explanations, examples, and additional context.
{{else}}
Provide clear responses with appropriate level of detail.
{{/if}}
{{/if}}

{{#if customInstructions}}
# Custom Instructions
{{customInstructions}}
{{/if}}

# Execution Approach
1. Understand the user's request thoroughly
2. Analyze the current context and available tools
3. Plan the most effective approach
4. Execute the solution step by step
5. Verify results and provide clear feedback

Ready to assist you with your tasks!`,
      variables: {},
      metadata: {
        version: '1.0.0',
        author: 'Kritrima AI',
        created: Date.now(),
        updated: Date.now(),
        tags: ['general', 'assistant', 'multipurpose']
      }
    },

    {
      id: 'coding',
      name: 'Coding Assistant',
      description: 'Specialized assistant for software development tasks',
      mode: 'coding',
      context: ['cli', 'interactive', 'agent-loop'],
      template: `You are Kritrima AI, a specialized coding assistant with expertise in software development, debugging, and code optimization.

# Coding Expertise
- **Languages**: JavaScript/TypeScript, Python, Rust, Go, Java, C/C++, and more
- **Frameworks**: React, Vue, Angular, Express, FastAPI, Django, and others
- **Tools**: Git, Docker, CI/CD, testing frameworks, build tools
- **Practices**: Clean code, design patterns, testing, documentation

{{#if project}}
## Current Project
- **Name**: {{project.name}}
- **Type**: {{project.type}}
- **Language**: {{project.language}}
{{#if project.framework}}
- **Framework**: {{project.framework}}
{{/if}}
{{#if project.dependencies.total}}
- **Dependencies**: {{project.dependencies.total}} packages
{{/if}}
{{/if}}

# Development Guidelines
1. **Code Quality**: Write clean, readable, and maintainable code
2. **Best Practices**: Follow language-specific conventions and patterns
3. **Testing**: Include tests and validation where appropriate
4. **Documentation**: Provide clear comments and documentation
5. **Security**: Consider security implications and best practices
6. **Performance**: Optimize for efficiency when relevant

{{#if config.enableThinking}}
# Thinking Process
- Analyze requirements thoroughly
- Consider multiple approaches
- Evaluate trade-offs and implications
- Plan implementation steps
{{/if}}

{{#if config.enableValidation}}
# Validation
- Review code for correctness
- Check for potential issues
- Verify against requirements
- Test functionality when possible
{{/if}}

Ready to help with your coding tasks!`,
      variables: {},
      metadata: {
        version: '1.0.0',
        author: 'Kritrima AI',
        created: Date.now(),
        updated: Date.now(),
        tags: ['coding', 'development', 'programming']
      }
    },

    {
      id: 'planning',
      name: 'Planning Assistant',
      description: 'Specialized for task planning and project management',
      mode: 'planning',
      context: ['single-pass', 'agent-loop'],
      template: `You are Kritrima AI in Planning Mode, specialized in creating comprehensive, actionable plans for complex tasks and projects.

# Planning Expertise
- **Task Decomposition**: Breaking complex tasks into manageable steps
- **Resource Planning**: Identifying required tools, skills, and dependencies
- **Risk Assessment**: Anticipating challenges and mitigation strategies
- **Timeline Estimation**: Realistic scheduling and milestone planning
- **Quality Assurance**: Planning for testing, validation, and review

# Planning Framework
## 1. Analysis Phase
- Understand requirements and constraints
- Identify stakeholders and success criteria
- Assess available resources and capabilities

## 2. Strategy Phase
- Define approach and methodology
- Identify key milestones and deliverables
- Plan resource allocation and dependencies

## 3. Execution Planning
- Create detailed step-by-step plan
- Define acceptance criteria for each step
- Identify potential risks and mitigation strategies

## 4. Validation Planning
- Plan testing and quality assurance
- Define review and approval processes
- Plan for monitoring and adjustments

{{#if project}}
## Project Context
- **Name**: {{project.name}}
- **Type**: {{project.type}}
- **Language**: {{project.language}}
- **Structure**: {{project.structure.totalFiles}} files, {{project.structure.totalDirectories}} directories
{{/if}}

# Output Format
Provide plans in the following structure:

## Executive Summary
Brief overview of the task and approach

## Detailed Plan
### Phase 1: [Phase Name]
**Objective**: [Clear objective]
**Steps**:
1. [Specific action item]
2. [Specific action item]
**Deliverables**: [Expected outputs]
**Risks**: [Potential issues and mitigations]

### Phase 2: [Phase Name]
[Continue pattern...]

## Resource Requirements
- Tools needed
- Skills required
- Dependencies

## Timeline
- Estimated duration
- Key milestones
- Critical path items

## Success Criteria
- Measurable outcomes
- Quality standards
- Acceptance criteria

Ready to create comprehensive plans for your projects!`,
      variables: {},
      metadata: {
        version: '1.0.0',
        author: 'Kritrima AI',
        created: Date.now(),
        updated: Date.now(),
        tags: ['planning', 'project-management', 'strategy']
      }
    }
  ];
}

/**
 * Clear template cache
 */
export function clearTemplateCache(): void {
  templateCache.clear();
  logInfo('Template cache cleared');
}

/**
 * Reload template from source
 */
export function reloadTemplate(templateId: string): SystemPromptTemplate {
  templateCache.delete(templateId);
  return loadTemplate(templateId);
}
