export async function* streamResponses(input, completion) {
    let accumulatedContent = '';
    let functionCall = null;
    try {
        yield {
            type: 'response.created',
            metadata: {
                model: input.model,
                timestamp: Date.now()
            }
        };
        for await (const chunk of completion) {
            const choice = chunk.choices?.[0];
            if (!choice)
                continue;
            if (choice.delta?.content) {
                accumulatedContent += choice.delta.content;
                yield {
                    type: 'response.output_text.delta',
                    delta: choice.delta.content,
                    content: accumulatedContent
                };
            }
            if (choice.delta?.tool_calls) {
                for (const toolCall of choice.delta.tool_calls) {
                    if (toolCall.function) {
                        if (!functionCall) {
                            functionCall = {
                                id: toolCall.id || '',
                                name: toolCall.function.name || '',
                                arguments: '',
                                type: 'function_call',
                                timestamp: Date.now()
                            };
                        }
                        if (toolCall.function.arguments) {
                            functionCall.arguments += toolCall.function.arguments;
                        }
                    }
                }
            }
            if (choice.finish_reason) {
                if (functionCall && functionCall.name) {
                    yield {
                        type: 'response.function_call_arguments.done',
                        functionCall: functionCall
                    };
                }
                yield {
                    type: 'response.completed',
                    content: accumulatedContent,
                    metadata: {
                        finish_reason: choice.finish_reason,
                        model: input.model,
                        timestamp: Date.now()
                    }
                };
                break;
            }
        }
    }
    catch (error) {
        yield {
            type: 'response.error',
            error: error instanceof Error ? error.message : 'Unknown error',
            metadata: {
                timestamp: Date.now()
            }
        };
    }
}
export function createResponseOutputItem(content, metadata) {
    return {
        role: 'assistant',
        content,
        type: 'output',
        timestamp: Date.now(),
        metadata
    };
}
export async function processStreamingCompletion(client, input, onDelta, onComplete, onError) {
    try {
        const completion = await client.chat.completions.create({
            ...input,
            stream: false
        });
        const choice = completion.choices?.[0];
        if (!choice) {
            throw new Error('No response choice received');
        }
        const content = choice.message?.content || '';
        onDelta?.(content);
        onComplete?.(content);
        return content;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        onError?.(errorMessage);
        throw error;
    }
}
export async function processCompletion(client, input) {
    try {
        const completion = await client.chat.completions.create({
            ...input,
            stream: false
        });
        const choice = completion.choices?.[0];
        if (!choice) {
            throw new Error('No response choice received');
        }
        const content = choice.message?.content || '';
        let functionCall;
        if (choice.message?.tool_calls?.[0]) {
            const toolCall = choice.message.tool_calls[0];
            if (toolCall.type === 'function') {
                functionCall = {
                    id: toolCall.id,
                    name: toolCall.function.name,
                    arguments: toolCall.function.arguments,
                    type: 'function_call',
                    timestamp: Date.now()
                };
            }
        }
        return {
            content,
            functionCall,
            usage: completion.usage
        };
    }
    catch (error) {
        throw error;
    }
}
export function convertMessagesToOpenAI(items) {
    const messages = [];
    for (const item of items) {
        if (item.type === 'input' || item.type === 'message') {
            const content = [];
            for (const contentItem of item.content) {
                if (contentItem.type === 'input_text' && contentItem.text) {
                    content.push({
                        type: 'text',
                        text: contentItem.text
                    });
                }
                else if (contentItem.type === 'input_image' && contentItem.image) {
                    content.push({
                        type: 'image_url',
                        image_url: {
                            url: contentItem.image.url,
                            detail: contentItem.image.detail || 'auto'
                        }
                    });
                }
                else if (contentItem.type === 'image' && contentItem.image) {
                    content.push({
                        type: 'image_url',
                        image_url: {
                            url: contentItem.image.url,
                            detail: contentItem.image.detail || 'auto'
                        }
                    });
                }
            }
            if (content.length > 0) {
                messages.push({
                    role: item.role,
                    content: content.length === 1 && content[0].type === 'text'
                        ? content[0].text
                        : content
                });
            }
        }
        else if (item.type === 'output') {
            if (item.content && item.content.trim()) {
                messages.push({
                    role: 'assistant',
                    content: item.content
                });
            }
        }
    }
    return messages;
}
export function estimateTokenCount(messages) {
    let totalTokens = 0;
    for (const message of messages) {
        totalTokens += 4;
        if (typeof message.content === 'string') {
            totalTokens += Math.ceil(message.content.length / 4);
        }
        else if (Array.isArray(message.content)) {
            for (const item of message.content) {
                if (item.type === 'text' && item.text) {
                    totalTokens += Math.ceil(item.text.length / 4);
                }
                else if (item.type === 'image_url') {
                    totalTokens += 170;
                }
            }
        }
    }
    return totalTokens;
}
export function debugMessageConversion(items) {
    const details = [];
    let inputItems = 0;
    let outputItems = 0;
    let functionCalls = 0;
    let toolResults = 0;
    for (const item of items) {
        const detail = {
            type: item.type,
            hasContent: false,
            contentTypes: []
        };
        if ('role' in item) {
            detail.role = item.role;
        }
        if (item.type === 'input' || item.type === 'message') {
            inputItems++;
            if ('content' in item && Array.isArray(item.content)) {
                detail.hasContent = item.content.length > 0;
                detail.contentTypes = item.content.map(c => c.type);
            }
        }
        else if (item.type === 'output') {
            outputItems++;
            detail.hasContent = !!item.content && item.content.trim();
        }
        else if (item.type === 'function_call') {
            functionCalls++;
        }
        else if (item.type === 'tool_result') {
            toolResults++;
        }
        details.push(detail);
    }
    const convertedMessages = convertMessagesToOpenAI(items);
    return {
        inputItems,
        outputItems,
        functionCalls,
        toolResults,
        convertedMessages: convertedMessages.length,
        details
    };
}
