export interface SystemContext {
    os: {
        platform: string;
        release: string;
        arch: string;
        type: string;
        version: string;
    };
    node: {
        version: string;
        platform: string;
        arch: string;
        execPath: string;
    };
    environment: {
        workingDirectory: string;
        homeDirectory: string;
        tempDirectory: string;
        pathSeparator: string;
        environmentVariables: Record<string, string>;
    };
    hardware: {
        cpuCount: number;
        totalMemory: number;
        freeMemory: number;
        cpuModel?: string;
    };
    capabilities: {
        hasGit: boolean;
        hasDocker: boolean;
        hasNode: boolean;
        hasNpm: boolean;
        hasPython: boolean;
        hasJava: boolean;
        availableShells: string[];
    };
    runtime: {
        timestamp: number;
        uptime: number;
        processId: number;
        parentProcessId?: number;
    };
    security: {
        isElevated: boolean;
        canWriteToSystem: boolean;
        sandboxed: boolean;
    };
}
export declare function getSystemContext(): Promise<SystemContext>;
//# sourceMappingURL=system-context.d.ts.map