/**
 * Prompt Builder
 * 
 * Fluent API for building complex system prompts with template processing,
 * variable substitution, context injection, and provider-specific optimization.
 */

import type { 
  SystemPromptTemplate, 
  SystemPromptConfig, 
  ProviderName 
} from '../../types/index.js';
import { logInfo, logError } from '../logger/log.js';

/**
 * Template variable pattern for replacement
 */
const VARIABLE_PATTERN = /\{\{([^}]+)\}\}/g;

/**
 * Provider-specific optimizations
 */
const PROVIDER_OPTIMIZATIONS = {
  openai: {
    maxTokens: 4096,
    supportsSystemRole: true,
    preferredFormat: 'markdown',
    specialInstructions: 'Use clear, structured responses with proper formatting.'
  },
  anthropic: {
    maxTokens: 8192,
    supportsSystemRole: true,
    preferredFormat: 'markdown',
    specialInstructions: 'Think step by step and provide detailed explanations.'
  },
  deepseek: {
    maxTokens: 4096,
    supportsSystemRole: true,
    preferredFormat: 'markdown',
    specialInstructions: 'Focus on accuracy and provide comprehensive solutions.'
  },
  gemini: {
    maxTokens: 2048,
    supportsSystemRole: false,
    preferredFormat: 'plain',
    specialInstructions: 'Be concise and direct in responses.'
  },
  ollama: {
    maxTokens: 2048,
    supportsSystemRole: true,
    preferredFormat: 'markdown',
    specialInstructions: 'Optimize for local processing efficiency.'
  }
};

/**
 * Prompt Builder Class
 */
export class PromptBuilder {
  private template: SystemPromptTemplate;
  private context: Record<string, any> = {};
  private config: SystemPromptConfig | null = null;
  private providerOptimization: ProviderName | null = null;
  private sections: string[] = [];
  private variables: Record<string, any> = {};

  constructor(template: SystemPromptTemplate) {
    this.template = template;
  }

  /**
   * Add context data for variable substitution
   */
  withContext(context: Record<string, any>): PromptBuilder {
    this.context = { ...this.context, ...context };
    return this;
  }

  /**
   * Add configuration
   */
  withConfig(config: SystemPromptConfig): PromptBuilder {
    this.config = config;
    return this;
  }

  /**
   * Enable provider-specific optimization
   */
  withProviderOptimization(provider: ProviderName): PromptBuilder {
    this.providerOptimization = provider;
    return this;
  }

  /**
   * Add custom section to prompt
   */
  addSection(title: string, content: string): PromptBuilder {
    this.sections.push(`# ${title}\n\n${content}`);
    return this;
  }

  /**
   * Add custom variables
   */
  withVariables(variables: Record<string, any>): PromptBuilder {
    this.variables = { ...this.variables, ...variables };
    return this;
  }

  /**
   * Build the final prompt
   */
  async build(): Promise<string> {
    try {
      logInfo('Building system prompt', { 
        templateId: this.template.id,
        provider: this.providerOptimization 
      });

      // Start with template
      let prompt = this.template.template;

      // Prepare all variables for substitution
      const allVariables = this.prepareVariables();

      // Perform variable substitution
      prompt = this.substituteVariables(prompt, allVariables);

      // Add custom sections
      if (this.sections.length > 0) {
        prompt += '\n\n' + this.sections.join('\n\n');
      }

      // Apply provider-specific optimizations
      if (this.providerOptimization) {
        prompt = this.applyProviderOptimizations(prompt, this.providerOptimization);
      }

      // Apply configuration-based modifications
      if (this.config) {
        prompt = this.applyConfigModifications(prompt, this.config);
      }

      // Final cleanup and formatting
      prompt = this.finalizePrompt(prompt);

      logInfo('System prompt built successfully', {
        templateId: this.template.id,
        promptLength: prompt.length,
        variableCount: Object.keys(allVariables).length
      });

      return prompt;

    } catch (error) {
      logError('Failed to build system prompt', error instanceof Error ? error : new Error(String(error)));
      
      // Return basic template as fallback
      return this.template.template;
    }
  }

  /**
   * Prepare all variables for substitution
   */
  private prepareVariables(): Record<string, any> {
    const variables: Record<string, any> = {};

    // Add context variables
    Object.assign(variables, this.context);

    // Add custom variables
    Object.assign(variables, this.variables);

    // Add system variables
    variables.timestamp = new Date().toISOString();
    variables.templateId = this.template.id;
    variables.templateVersion = this.template.metadata.version;

    // Flatten nested objects for dot notation access
    this.flattenObject(variables, variables);

    return variables;
  }

  /**
   * Flatten nested objects for dot notation access
   */
  private flattenObject(obj: any, target: Record<string, any>, prefix = ''): void {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          this.flattenObject(obj[key], target, newKey);
        } else {
          target[newKey] = obj[key];
        }
      }
    }
  }

  /**
   * Substitute variables in template
   */
  private substituteVariables(template: string, variables: Record<string, any>): string {
    return template.replace(VARIABLE_PATTERN, (match, variablePath) => {
      const value = this.getNestedValue(variables, variablePath.trim());
      
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          return JSON.stringify(value, null, 2);
        }
        return String(value);
      }
      
      // Return placeholder if variable not found
      return `[${variablePath}]`;
    });
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: Record<string, any>, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Apply provider-specific optimizations
   */
  private applyProviderOptimizations(prompt: string, provider: ProviderName): string {
    const optimization = PROVIDER_OPTIMIZATIONS[provider];
    
    if (!optimization) {
      return prompt;
    }

    let optimizedPrompt = prompt;

    // Add provider-specific instructions
    if (optimization.specialInstructions) {
      optimizedPrompt += `\n\n# Provider Optimization\n${optimization.specialInstructions}`;
    }

    // Format based on provider preferences
    if (optimization.preferredFormat === 'plain') {
      // Remove markdown formatting for providers that don't support it well
      optimizedPrompt = optimizedPrompt
        .replace(/^#+\s/gm, '')
        .replace(/\*\*(.*?)\*\*/g, '$1')
        .replace(/\*(.*?)\*/g, '$1');
    }

    // Handle system role support
    if (!optimization.supportsSystemRole) {
      optimizedPrompt = `Instructions: ${optimizedPrompt}`;
    }

    return optimizedPrompt;
  }

  /**
   * Apply configuration-based modifications
   */
  private applyConfigModifications(prompt: string, config: SystemPromptConfig): string {
    let modifiedPrompt = prompt;

    // Add safety level instructions
    if (config.safetyLevel) {
      const safetyInstructions = this.getSafetyInstructions(config.safetyLevel);
      modifiedPrompt += `\n\n# Safety Guidelines\n${safetyInstructions}`;
    }

    // Add verbosity instructions
    if (config.verbosityLevel) {
      const verbosityInstructions = this.getVerbosityInstructions(config.verbosityLevel);
      modifiedPrompt += `\n\n# Response Style\n${verbosityInstructions}`;
    }

    // Add thinking instructions
    if (config.enableThinking) {
      modifiedPrompt += '\n\n# Thinking Process\nThink through problems step by step before providing solutions.';
    }

    // Add planning instructions
    if (config.enablePlanning) {
      modifiedPrompt += '\n\n# Planning\nCreate detailed plans for complex tasks before execution.';
    }

    // Add validation instructions
    if (config.enableValidation) {
      modifiedPrompt += '\n\n# Validation\nValidate your work and double-check results before finalizing.';
    }

    // Add custom instructions
    if (config.customInstructions) {
      modifiedPrompt += `\n\n# Custom Instructions\n${config.customInstructions}`;
    }

    return modifiedPrompt;
  }

  /**
   * Get safety instructions based on level
   */
  private getSafetyInstructions(level: string): string {
    switch (level) {
      case 'strict':
        return 'Always prioritize safety. Refuse potentially harmful requests. Ask for confirmation before executing system commands.';
      case 'moderate':
        return 'Exercise caution with system operations. Warn about potentially risky actions.';
      case 'permissive':
        return 'Proceed with user requests while noting any potential risks.';
      default:
        return 'Follow standard safety practices.';
    }
  }

  /**
   * Get verbosity instructions based on level
   */
  private getVerbosityInstructions(level: string): string {
    switch (level) {
      case 'minimal':
        return 'Provide concise, direct responses with minimal explanation.';
      case 'normal':
        return 'Provide clear responses with appropriate level of detail.';
      case 'detailed':
        return 'Provide comprehensive responses with detailed explanations.';
      case 'verbose':
        return 'Provide extensive responses with thorough explanations and examples.';
      default:
        return 'Provide clear and helpful responses.';
    }
  }

  /**
   * Final prompt cleanup and formatting
   */
  private finalizePrompt(prompt: string): string {
    return prompt
      .replace(/\n{3,}/g, '\n\n') // Remove excessive newlines
      .replace(/^\s+|\s+$/g, '') // Trim whitespace
      .replace(/\[([^\]]+)\]/g, '') // Remove unfilled placeholders
      + '\n'; // Ensure ending newline
  }
}

/**
 * Quick helper to build prompt from template
 */
export async function buildPrompt(
  template: SystemPromptTemplate,
  context: Record<string, any> = {},
  config?: SystemPromptConfig,
  provider?: ProviderName
): Promise<string> {
  const builder = new PromptBuilder(template);
  
  builder.withContext(context);
  
  if (config) {
    builder.withConfig(config);
  }
  
  if (provider) {
    builder.withProviderOptimization(provider);
  }
  
  return builder.build();
}
