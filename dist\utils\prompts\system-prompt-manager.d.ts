import type { SystemPromptConfig, SystemPromptTemplate, SystemPromptResult, SystemPromptContext as SystemPromptContextType, SystemPromptMode, AppConfig } from '../../types/index.js';
export declare class SystemPromptManager {
    private templates;
    private contextCache;
    private config;
    constructor(config: AppConfig);
    generateSystemPrompt(config: SystemPromptConfig): Promise<SystemPromptResult>;
    private getTemplate;
    private gatherContext;
    private loadTemplates;
    private createFallbackTemplate;
    private generateFallbackPrompt;
    private estimateTokenCount;
    clearCache(): void;
    getAvailableTemplates(): SystemPromptTemplate[];
    updateConfig(config: AppConfig): void;
}
export declare function getSystemPromptManager(config: AppConfig): SystemPromptManager;
export declare function generateSystemPrompt(mode: SystemPromptMode, context: SystemPromptContextType, config: AppConfig, options?: Partial<SystemPromptConfig>): Promise<SystemPromptResult>;
//# sourceMappingURL=system-prompt-manager.d.ts.map