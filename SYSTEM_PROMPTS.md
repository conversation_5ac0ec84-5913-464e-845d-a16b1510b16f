# System Prompts Documentation

The Kritrima AI CLI features a comprehensive system prompt management system that provides context-aware, dynamic prompts for different use cases and scenarios.

## Overview

The system prompt system consists of several key components:

- **System Prompt Manager**: Central management of prompts with dynamic generation
- **Template System**: Modular, reusable prompt templates
- **Context Providers**: Automatic injection of system, project, and session context
- **Prompt Builder**: Fluent API for building complex prompts
- **CLI Integration**: Command-line tools for prompt management

## Features

### 🎯 **Context-Aware Prompts**
- Automatically adapts to your operating system, project type, and available tools
- Includes relevant project information (dependencies, configuration, structure)
- Provides session context and user preferences

### 🔧 **Multiple Prompt Modes**
- **General**: All-purpose assistant for various tasks
- **Coding**: Specialized for software development
- **Planning**: Focused on task planning and project management
- **Analysis**: Optimized for data analysis and investigation
- **Debugging**: Specialized for troubleshooting and problem-solving
- **Documentation**: Focused on writing and documentation tasks
- **Testing**: Optimized for test creation and validation

### 🌍 **Context Types**
- **CLI**: Command-line interface usage
- **Interactive**: Real-time conversation mode
- **Single-pass**: One-time task execution
- **Agent-loop**: Multi-step autonomous operations
- **Batch**: Bulk processing scenarios
- **Streaming**: Real-time data processing

### ⚙️ **Configurable Options**
- **Safety Levels**: Strict, moderate, or permissive operation modes
- **Verbosity Levels**: Minimal, normal, detailed, or verbose responses
- **Custom Instructions**: User-defined additional guidance
- **Provider Optimization**: Tailored for different AI providers

## Usage

### Command Line Options

```bash
# List available prompt templates
kritrima-ai --list-prompts

# Show details of a specific template
kritrima-ai --show-prompt general

# Set prompt mode
kritrima-ai --prompt-mode coding

# Set context type
kritrima-ai --prompt-context agent-loop

# Configure safety level
kritrima-ai --safety-level strict

# Set verbosity level
kritrima-ai --verbosity detailed

# Add custom instructions
kritrima-ai --custom-instructions "Focus on TypeScript and React development"

# Combine multiple options
kritrima-ai --prompt-mode coding --safety-level moderate --verbosity detailed
```

### Configuration File

Add system prompt settings to your configuration:

```json
{
  "systemPromptMode": "coding",
  "systemPromptContext": "interactive",
  "safetyLevel": "moderate",
  "verbosityLevel": "normal",
  "customInstructions": "Always provide TypeScript examples and focus on best practices"
}
```

### Programmatic Usage

```javascript
import { generateSystemPrompt } from './src/utils/prompts/system-prompt-manager.js';

const result = await generateSystemPrompt(
  'coding',           // mode
  'interactive',      // context
  config,            // app configuration
  {
    safetyLevel: 'moderate',
    verbosityLevel: 'detailed',
    customInstructions: 'Focus on React development',
    enableThinking: true,
    enablePlanning: false
  }
);

console.log(result.prompt);
console.log(`Generated ${result.metadata.tokenCount} tokens`);
```

## Available Templates

### General Template
- **Mode**: `general`
- **Context**: `cli`, `interactive`, `agent-loop`
- **Description**: All-purpose assistant for various tasks
- **Features**: Comprehensive capabilities, adaptive responses

### Coding Template
- **Mode**: `coding`
- **Context**: `cli`, `interactive`, `agent-loop`
- **Description**: Specialized for software development
- **Features**: Code quality focus, best practices, testing guidance

### Planning Template
- **Mode**: `planning`
- **Context**: `single-pass`, `agent-loop`
- **Description**: Task planning and project management
- **Features**: Structured planning framework, risk assessment

## Context Information

The system automatically includes relevant context:

### System Context
- Operating system and architecture
- Node.js version and runtime information
- Available tools and capabilities
- Hardware specifications
- Security and permission information

### Project Context
- Project name, type, and language
- Dependencies and configuration
- File structure and organization
- Git repository information
- Build tools and frameworks

### Session Context
- Session duration and activity
- Command history and patterns
- User preferences and expertise
- Recent files and projects

### Tool Context
- Available functions and tools
- Permission levels and restrictions
- Execution capabilities
- Safety constraints

## Template Variables

Templates support variable substitution using Handlebars-like syntax:

```handlebars
{{system.os.platform}}           # Operating system
{{project.name}}                 # Project name
{{project.language}}             # Programming language
{{config.safetyLevel}}           # Safety configuration
{{customInstructions}}           # User instructions
{{tools.available}}              # Available tools
```

## Creating Custom Templates

Create custom templates in `.kritrima/templates/`:

```json
{
  "id": "my-template",
  "name": "My Custom Template",
  "description": "Specialized template for my use case",
  "mode": "custom",
  "context": ["interactive"],
  "template": "You are a specialized assistant for...\n\n{{customInstructions}}",
  "variables": {},
  "metadata": {
    "version": "1.0.0",
    "author": "Your Name",
    "created": 1703097600000,
    "updated": 1703097600000,
    "tags": ["custom", "specialized"]
  }
}
```

## Safety Levels

### Strict
- Always prioritize safety over convenience
- Require explicit confirmation for system modifications
- Refuse potentially harmful requests
- Conservative approach to all operations

### Moderate (Default)
- Exercise caution with system operations
- Warn about potential risks
- Ask for confirmation on significant changes
- Balanced approach to safety and functionality

### Permissive
- Proceed with user requests while noting risks
- Provide warnings for potentially dangerous operations
- Allow more autonomous operation
- Trust user judgment

## Verbosity Levels

### Minimal
- Concise, direct responses
- Essential information only
- Minimal explanations
- Quick task completion

### Normal (Default)
- Clear responses with appropriate detail
- Balanced information level
- Standard explanations
- Good for most use cases

### Detailed
- Comprehensive responses
- Detailed explanations and examples
- Step-by-step guidance
- Educational approach

### Verbose
- Extensive responses
- Thorough explanations
- Additional context and examples
- Maximum information

## Best Practices

1. **Choose the Right Mode**: Select the prompt mode that matches your task
2. **Set Appropriate Safety**: Use strict mode for system operations, moderate for general use
3. **Customize Instructions**: Add specific guidance for your domain or preferences
4. **Use Context Appropriately**: Match context to your interaction pattern
5. **Monitor Performance**: Check token usage and adjust verbosity as needed

## Troubleshooting

### Template Not Found
- Check available templates with `--list-prompts`
- Verify template ID spelling
- Ensure template files are properly formatted

### Context Generation Errors
- Check system permissions
- Verify project structure
- Review configuration settings

### Performance Issues
- Reduce verbosity level
- Simplify custom instructions
- Use minimal context when possible

## Testing

Run the system prompt test suite:

```bash
node test-system-prompt.js
```

This will verify:
- Template loading functionality
- Prompt generation for different modes
- Context injection
- Error handling
- Custom instruction processing
