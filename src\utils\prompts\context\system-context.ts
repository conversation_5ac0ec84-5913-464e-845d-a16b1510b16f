/**
 * System Context Provider
 * 
 * Provides system-level context information including OS details,
 * environment variables, capabilities, and runtime information.
 */

import { platform, release, arch, homedir, tmpdir, cpus, totalmem, freemem } from 'os';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { execSync } from 'child_process';
import { logInfo, logError } from '../../logger/log.js';

/**
 * System context interface
 */
export interface SystemContext {
  os: {
    platform: string;
    release: string;
    arch: string;
    type: string;
    version: string;
  };
  node: {
    version: string;
    platform: string;
    arch: string;
    execPath: string;
  };
  environment: {
    workingDirectory: string;
    homeDirectory: string;
    tempDirectory: string;
    pathSeparator: string;
    environmentVariables: Record<string, string>;
  };
  hardware: {
    cpuCount: number;
    totalMemory: number;
    freeMemory: number;
    cpuModel?: string;
  };
  capabilities: {
    hasGit: boolean;
    hasDocker: boolean;
    hasNode: boolean;
    hasNpm: boolean;
    hasPython: boolean;
    hasJava: boolean;
    availableShells: string[];
  };
  runtime: {
    timestamp: number;
    uptime: number;
    processId: number;
    parentProcessId?: number;
  };
  security: {
    isElevated: boolean;
    canWriteToSystem: boolean;
    sandboxed: boolean;
  };
}

/**
 * Get comprehensive system context
 */
export async function getSystemContext(): Promise<SystemContext> {
  try {
    logInfo('Gathering system context');

    const context: SystemContext = {
      os: await getOSInfo(),
      node: getNodeInfo(),
      environment: getEnvironmentInfo(),
      hardware: getHardwareInfo(),
      capabilities: await getCapabilities(),
      runtime: getRuntimeInfo(),
      security: await getSecurityInfo()
    };

    logInfo('System context gathered successfully');
    return context;

  } catch (error) {
    logError('Failed to gather system context', error instanceof Error ? error : new Error(String(error)));
    
    // Return minimal context as fallback
    return getMinimalContext();
  }
}

/**
 * Get operating system information
 */
async function getOSInfo() {
  const osInfo = {
    platform: platform(),
    release: release(),
    arch: arch(),
    type: platform(),
    version: release()
  };

  // Try to get more detailed OS information
  try {
    if (platform() === 'linux') {
      if (existsSync('/etc/os-release')) {
        const osRelease = readFileSync('/etc/os-release', 'utf8');
        const nameMatch = osRelease.match(/PRETTY_NAME="([^"]+)"/);
        if (nameMatch) {
          osInfo.version = nameMatch[1];
        }
      }
    } else if (platform() === 'darwin') {
      try {
        const version = execSync('sw_vers -productVersion', { encoding: 'utf8' }).trim();
        osInfo.version = `macOS ${version}`;
      } catch {
        // Ignore error
      }
    } else if (platform() === 'win32') {
      try {
        const version = execSync('ver', { encoding: 'utf8' }).trim();
        osInfo.version = version;
      } catch {
        // Ignore error
      }
    }
  } catch {
    // Use default values
  }

  return osInfo;
}

/**
 * Get Node.js runtime information
 */
function getNodeInfo() {
  return {
    version: process.version,
    platform: process.platform,
    arch: process.arch,
    execPath: process.execPath
  };
}

/**
 * Get environment information
 */
function getEnvironmentInfo() {
  // Filter sensitive environment variables
  const sensitiveKeys = ['PASSWORD', 'SECRET', 'KEY', 'TOKEN', 'API_KEY', 'AUTH'];
  const filteredEnv: Record<string, string> = {};
  
  for (const [key, value] of Object.entries(process.env)) {
    if (value && !sensitiveKeys.some(sensitive => key.toUpperCase().includes(sensitive))) {
      filteredEnv[key] = value;
    }
  }

  return {
    workingDirectory: process.cwd(),
    homeDirectory: homedir(),
    tempDirectory: tmpdir(),
    pathSeparator: platform() === 'win32' ? '\\' : '/',
    environmentVariables: filteredEnv
  };
}

/**
 * Get hardware information
 */
function getHardwareInfo() {
  const cpuInfo = cpus();
  
  return {
    cpuCount: cpuInfo.length,
    totalMemory: totalmem(),
    freeMemory: freemem(),
    cpuModel: cpuInfo[0]?.model
  };
}

/**
 * Get system capabilities
 */
async function getCapabilities() {
  const capabilities = {
    hasGit: false,
    hasDocker: false,
    hasNode: true, // We're running in Node
    hasNpm: false,
    hasPython: false,
    hasJava: false,
    availableShells: [] as string[]
  };

  // Check for various tools
  const tools = [
    { name: 'git', command: 'git --version', key: 'hasGit' },
    { name: 'docker', command: 'docker --version', key: 'hasDocker' },
    { name: 'npm', command: 'npm --version', key: 'hasNpm' },
    { name: 'python', command: 'python --version', key: 'hasPython' },
    { name: 'java', command: 'java -version', key: 'hasJava' }
  ];

  for (const tool of tools) {
    try {
      execSync(tool.command, { stdio: 'ignore', timeout: 5000 });
      (capabilities as any)[tool.key] = true;
    } catch {
      // Tool not available
    }
  }

  // Check available shells
  const shells = platform() === 'win32' 
    ? ['cmd', 'powershell', 'pwsh']
    : ['bash', 'sh', 'zsh', 'fish'];

  for (const shell of shells) {
    try {
      execSync(platform() === 'win32' ? `where ${shell}` : `which ${shell}`, { 
        stdio: 'ignore', 
        timeout: 2000 
      });
      capabilities.availableShells.push(shell);
    } catch {
      // Shell not available
    }
  }

  return capabilities;
}

/**
 * Get runtime information
 */
function getRuntimeInfo() {
  return {
    timestamp: Date.now(),
    uptime: process.uptime() * 1000, // Convert to milliseconds
    processId: process.pid,
    parentProcessId: process.ppid
  };
}

/**
 * Get security information
 */
async function getSecurityInfo() {
  const security = {
    isElevated: false,
    canWriteToSystem: false,
    sandboxed: false
  };

  try {
    // Check if running with elevated privileges
    if (platform() === 'win32') {
      try {
        execSync('net session', { stdio: 'ignore', timeout: 2000 });
        security.isElevated = true;
      } catch {
        // Not elevated
      }
    } else {
      security.isElevated = process.getuid ? process.getuid() === 0 : false;
    }

    // Check if can write to system directories
    const systemPaths = platform() === 'win32' 
      ? ['C:\\Windows\\System32']
      : ['/usr/bin', '/etc'];

    for (const path of systemPaths) {
      try {
        if (existsSync(path)) {
          // Try to create a temporary file (this is a read-only check)
          security.canWriteToSystem = false; // Conservative approach
          break;
        }
      } catch {
        // Cannot write
      }
    }

    // Check for sandboxing (basic detection)
    security.sandboxed = !!(
      process.env.SANDBOX ||
      process.env.FLATPAK_ID ||
      process.env.SNAP ||
      process.env.APPIMAGE
    );

  } catch (error) {
    logError('Failed to get security info', error instanceof Error ? error : new Error(String(error)));
  }

  return security;
}

/**
 * Get minimal context as fallback
 */
function getMinimalContext(): SystemContext {
  return {
    os: {
      platform: platform(),
      release: release(),
      arch: arch(),
      type: platform(),
      version: release()
    },
    node: {
      version: process.version,
      platform: process.platform,
      arch: process.arch,
      execPath: process.execPath
    },
    environment: {
      workingDirectory: process.cwd(),
      homeDirectory: homedir(),
      tempDirectory: tmpdir(),
      pathSeparator: platform() === 'win32' ? '\\' : '/',
      environmentVariables: {}
    },
    hardware: {
      cpuCount: 1,
      totalMemory: 0,
      freeMemory: 0
    },
    capabilities: {
      hasGit: false,
      hasDocker: false,
      hasNode: true,
      hasNpm: false,
      hasPython: false,
      hasJava: false,
      availableShells: []
    },
    runtime: {
      timestamp: Date.now(),
      uptime: 0,
      processId: process.pid
    },
    security: {
      isElevated: false,
      canWriteToSystem: false,
      sandboxed: false
    }
  };
}
